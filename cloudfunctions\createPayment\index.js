// 云函数：创建支付订单
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { openid } = wxContext
  
  try {
    const { planId, amount, description } = event
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }
    
    // 生成订单号
    const orderNo = generateOrderNo()
    
    // 创建订单记录
    await db.collection('orders').add({
      data: {
        orderNo: orderNo,
        openid: openid,
        planId: planId,
        amount: amount,
        description: description,
        status: 'pending',
        createTime: new Date()
      }
    })
    
    // 调用微信支付统一下单API
    const paymentResult = await cloud.cloudPay.unifiedOrder({
      body: description,
      outTradeNo: orderNo,
      spbillCreateIp: '127.0.0.1',
      subMchId: '', // 子商户号，如果是服务商模式需要填写
      totalFee: amount,
      envId: cloud.DYNAMIC_CURRENT_ENV,
      functionName: 'payCallback' // 支付回调云函数名
    })
    
    return {
      success: true,
      payment: paymentResult.payment,
      orderNo: orderNo
    }
    
  } catch (error) {
    console.error('创建支付订单失败:', error)
    return {
      success: false,
      message: '创建订单失败，请重试'
    }
  }
}

// 生成订单号
function generateOrderNo() {
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  return `IVD${timestamp}${random}`
}
