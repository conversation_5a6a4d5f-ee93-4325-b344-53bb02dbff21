// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    features: [
      {
        icon: '🔬',
        title: '产品研发咨询',
        desc: '获取专业的产品研发流程指导',
        color: '#FF6B6B'
      },
      {
        icon: '📋',
        title: '注册申报指导',
        desc: '了解注册申报要点和流程',
        color: '#4ECDC4'
      },
      {
        icon: '📈',
        title: '市场销售策略',
        desc: '制定有效的市场销售方案',
        color: '#45B7D1'
      },
      {
        icon: '🤖',
        title: '智能AI助手',
        desc: '多模型AI为您提供专业建议',
        color: '#96CEB4'
      }
    ],
    aiModels: [
      { name: 'DeepSeek-V3', desc: '深度推理能力强', available: true },
      { name: 'DeepSeek-R1', desc: '逻辑推理专家', available: true },
      { name: 'Qwen3', desc: '中文理解优秀', available: false },
      { name: 'ChatGPT', desc: '通用对话模型', available: false }
    ]
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = app.globalData.userInfo
    const isLoggedIn = app.globalData.isLoggedIn
    
    this.setData({
      userInfo,
      isLoggedIn
    })
  },

  // 开始聊天
  startChat() {
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再使用AI咨询功能',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    wx.switchTab({
      url: '/pages/chat/chat'
    })
  },

  // 查看订阅
  viewSubscription() {
    if (!this.data.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后查看订阅信息',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    wx.navigateTo({
      url: '/pages/subscription/subscription'
    })
  },

  // 功能卡片点击
  onFeatureClick(e) {
    const index = e.currentTarget.dataset.index
    const feature = this.data.features[index]
    
    wx.showToast({
      title: `点击了${feature.title}`,
      icon: 'none'
    })
    
    // 可以根据不同功能跳转到不同页面或设置不同的聊天模式
    this.startChat()
  }
})
