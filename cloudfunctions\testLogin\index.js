// 测试云函数：验证登录环境
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  try {
    console.log('测试云函数开始执行')
    
    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    console.log('微信上下文:', wxContext)
    
    // 获取环境信息
    const envInfo = {
      env: cloud.DYNAMIC_CURRENT_ENV,
      timestamp: new Date().toISOString(),
      context: context
    }
    console.log('环境信息:', envInfo)
    
    // 测试数据库连接
    const db = cloud.database()
    let dbTest = null
    try {
      const collections = await db.collection('users').limit(1).get()
      dbTest = {
        success: true,
        message: '数据库连接正常',
        count: collections.data.length
      }
    } catch (dbError) {
      dbTest = {
        success: false,
        message: '数据库连接失败',
        error: dbError.message
      }
    }
    
    // 测试openapi
    let openapiTest = null
    if (event.testCode) {
      try {
        const authResult = await cloud.openapi.sns.jscode2session({
          jsCode: event.testCode,
          grantType: 'authorization_code'
        })
        openapiTest = {
          success: true,
          message: 'openapi调用成功',
          hasOpenid: !!authResult.openid
        }
      } catch (apiError) {
        openapiTest = {
          success: false,
          message: 'openapi调用失败',
          error: apiError.message
        }
      }
    }
    
    return {
      success: true,
      message: '测试完成',
      data: {
        wxContext,
        envInfo,
        dbTest,
        openapiTest,
        event
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error)
    return {
      success: false,
      message: '测试失败',
      error: error.message,
      stack: error.stack
    }
  }
}
