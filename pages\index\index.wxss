/* pages/index/index.wxss */

/* 头部欢迎区域 */
.header-section {
  margin-bottom: 40rpx;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  position: relative;
  overflow: hidden;
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.welcome-content {
  margin-bottom: 40rpx;
}

.title-main {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 10rpx;
}

.title-sub {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 20rpx;
}

.welcome-desc {
  font-size: 30rpx;
  opacity: 0.8;
  line-height: 1.5;
}

.start-chat-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.btn-icon {
  font-size: 36rpx;
}

/* 功能特色区域 */
.features-section {
  margin-bottom: 40rpx;
}

.section-title {
  margin-bottom: 30rpx;
  text-align: center;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.title-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.feature-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.feature-arrow {
  font-size: 32rpx;
  color: #cccccc;
  margin-left: 20rpx;
}

/* AI模型区域 */
.ai-models-section {
  margin-bottom: 40rpx;
}

.models-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.model-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.model-item:last-child {
  border-bottom: none;
}

.model-item.unavailable {
  opacity: 0.6;
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.model-desc {
  font-size: 26rpx;
  color: #666666;
}

.model-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.status-text {
  font-size: 24rpx;
  color: #666666;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.status-dot.active {
  background: #4CAF50;
}

.status-dot.inactive {
  background: #cccccc;
}

/* 订阅状态区域 */
.subscription-section {
  margin-bottom: 40rpx;
}

.subscription-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.subscription-title {
  font-size: 30rpx;
  opacity: 0.9;
}

.subscription-type {
  font-size: 32rpx;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
}

.subscription-quota {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.quota-text {
  font-size: 28rpx;
  opacity: 0.9;
}

.quota-number {
  font-size: 36rpx;
  font-weight: 700;
}

.upgrade-btn {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

/* 登录提示区域 */
.login-prompt {
  text-align: center;
}

.prompt-content {
  margin-bottom: 30rpx;
}

.prompt-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.prompt-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.prompt-desc {
  font-size: 26rpx;
  color: #666666;
}
