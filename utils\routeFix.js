// 路由修复工具
const RouteFix = {
  // 检查所有页面路由
  checkAllRoutes() {
    console.log('🔍 检查页面路由...')
    
    const pages = [
      'pages/index/index',
      'pages/chat/chat', 
      'pages/profile/profile',
      'pages/login/login',
      'pages/subscription/subscription'
    ]
    
    const results = {
      valid: [],
      invalid: []
    }
    
    pages.forEach(page => {
      try {
        // 尝试预加载页面来检查是否存在
        wx.navigateTo({
          url: `/${page}?test=true`,
          success: () => {
            results.valid.push(page)
            // 立即返回，避免实际跳转
            wx.navigateBack()
          },
          fail: (err) => {
            results.invalid.push({ page, error: err })
            console.error(`❌ 页面路由错误: ${page}`, err)
          }
        })
      } catch (error) {
        results.invalid.push({ page, error })
      }
    })
    
    console.log('✅ 有效路由:', results.valid)
    console.log('❌ 无效路由:', results.invalid)
    
    return results
  },

  // 修复路由问题
  async fixRoutes() {
    console.log('🔧 开始修复路由问题...')
    
    const fixes = []
    
    // 1. 检查基础库版本
    try {
      const systemInfo = wx.getSystemInfoSync()
      console.log('当前基础库版本:', systemInfo.SDKVersion)
      
      if (this.compareVersion(systemInfo.SDKVersion, '2.32.3') > 0) {
        fixes.push('建议降低基础库版本到 2.32.3')
      }
    } catch (e) {
      console.warn('无法获取系统信息:', e)
    }
    
    // 2. 检查页面文件完整性
    const requiredFiles = [
      'pages/index/index.js',
      'pages/index/index.wxml', 
      'pages/index/index.wxss',
      'pages/index/index.json'
    ]
    
    // 3. 重新初始化路由
    try {
      this.reinitializeRoutes()
      fixes.push('路由重新初始化完成')
    } catch (e) {
      fixes.push('路由重新初始化失败: ' + e.message)
    }
    
    console.log('🎯 修复结果:', fixes)
    return fixes
  },

  // 重新初始化路由
  reinitializeRoutes() {
    // 清理可能的路由缓存
    try {
      // 获取当前页面栈
      const pages = getCurrentPages()
      console.log('当前页面栈:', pages.length)
      
      // 如果页面栈为空或有问题，尝试重新导航到首页
      if (pages.length === 0) {
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }
    } catch (error) {
      console.error('路由重新初始化失败:', error)
      throw error
    }
  },

  // 版本比较函数
  compareVersion(v1, v2) {
    const arr1 = v1.split('.')
    const arr2 = v2.split('.')
    const length = Math.max(arr1.length, arr2.length)
    
    for (let i = 0; i < length; i++) {
      const num1 = parseInt(arr1[i] || '0')
      const num2 = parseInt(arr2[i] || '0')
      
      if (num1 > num2) return 1
      if (num1 < num2) return -1
    }
    
    return 0
  },

  // 安全导航函数
  safeNavigate(url, options = {}) {
    return new Promise((resolve, reject) => {
      const defaultOptions = {
        url,
        success: resolve,
        fail: reject,
        ...options
      }
      
      try {
        wx.navigateTo(defaultOptions)
      } catch (error) {
        console.error('导航失败:', error)
        reject(error)
      }
    })
  },

  // 修复并重启应用
  async fixAndRestart() {
    console.log('🚀 开始修复并重启应用...')
    
    try {
      // 1. 执行修复
      await this.fixRoutes()
      
      // 2. 清理应用状态
      this.clearAppState()
      
      // 3. 重新启动到首页
      wx.reLaunch({
        url: '/pages/index/index',
        success: () => {
          console.log('✅ 应用重启成功')
          wx.showToast({
            title: '修复完成',
            icon: 'success'
          })
        },
        fail: (err) => {
          console.error('❌ 应用重启失败:', err)
          wx.showToast({
            title: '重启失败，请手动重启',
            icon: 'none'
          })
        }
      })
      
    } catch (error) {
      console.error('修复过程失败:', error)
      throw error
    }
  },

  // 清理应用状态
  clearAppState() {
    try {
      // 清理可能导致路由问题的缓存
      const keysToRemove = []
      const info = wx.getStorageInfoSync()
      
      info.keys.forEach(key => {
        if (key.includes('route') || key.includes('page') || key.includes('nav')) {
          keysToRemove.push(key)
        }
      })
      
      keysToRemove.forEach(key => {
        wx.removeStorageSync(key)
      })
      
      console.log(`🗑️ 已清理 ${keysToRemove.length} 个路由相关缓存`)
    } catch (error) {
      console.warn('清理应用状态失败:', error)
    }
  },

  // 诊断路由问题
  diagnoseRouteIssues() {
    console.log('🔍 诊断路由问题...')
    
    const diagnosis = {
      currentPages: [],
      systemInfo: null,
      appConfig: null,
      issues: []
    }
    
    try {
      // 获取当前页面栈
      diagnosis.currentPages = getCurrentPages().map(page => page.route)
    } catch (e) {
      diagnosis.issues.push('无法获取当前页面栈: ' + e.message)
    }
    
    try {
      // 获取系统信息
      diagnosis.systemInfo = wx.getSystemInfoSync()
    } catch (e) {
      diagnosis.issues.push('无法获取系统信息: ' + e.message)
    }
    
    try {
      // 检查应用配置
      const app = getApp()
      diagnosis.appConfig = {
        globalData: app.globalData,
        isLoggedIn: app.globalData?.isLoggedIn
      }
    } catch (e) {
      diagnosis.issues.push('无法获取应用配置: ' + e.message)
    }
    
    console.log('📊 诊断结果:', diagnosis)
    return diagnosis
  }
}

module.exports = RouteFix
