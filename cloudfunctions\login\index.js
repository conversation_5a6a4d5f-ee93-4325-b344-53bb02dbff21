// 云函数：用户登录
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { code, userInfo } = event
    
    // 获取用户的 openid 和 unionid
    const { openid, unionid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '获取用户信息失败'
      }
    }
    
    // 查询用户是否已存在
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    let userData = {
      openid: openid,
      unionid: unionid,
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      lastLoginTime: new Date(),
      subscription: {
        type: 'free',
        remainingQuota: 10,
        expiryDate: null
      }
    }
    
    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      userData.createTime = new Date()
      userData.totalUsage = 0
      
      await db.collection('users').add({
        data: userData
      })
    } else {
      // 老用户，更新登录时间和用户信息
      await db.collection('users').where({
        openid: openid
      }).update({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          lastLoginTime: new Date()
        }
      })
      
      // 获取用户的订阅信息
      const existingUser = userQuery.data[0]
      userData.subscription = existingUser.subscription || userData.subscription
      userData.totalUsage = existingUser.totalUsage || 0
    }
    
    return {
      success: true,
      openid: openid,
      userInfo: {
        nickName: userData.nickName,
        avatarUrl: userData.avatarUrl,
        openid: openid
      },
      subscription: userData.subscription
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败，请重试'
    }
  }
}
