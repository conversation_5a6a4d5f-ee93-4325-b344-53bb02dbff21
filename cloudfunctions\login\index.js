// 云函数：用户登录
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('登录云函数开始执行', {
      event,
      context,
      env: cloud.DYNAMIC_CURRENT_ENV
    })

    const { code, userInfo } = event

    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    console.log('微信上下文', wxContext)

    // 获取用户的 openid 和 unionid（注意：wxContext 中的属性是大写的）
    const openid = wxContext.OPENID
    const unionid = wxContext.UNIONID
    const appid = wxContext.APPID
    const env = wxContext.ENV

    console.log('获取到的用户标识', { openid, unionid, appid, env })

    // 检查openid是否存在
    if (!openid) {
      console.error('openid获取失败，wxContext:', wxContext)

      return {
        success: false,
        message: '获取用户信息失败，请重试',
        debug: {
          wxContext,
          hasCode: !!code,
          timestamp: new Date().toISOString()
        }
      }
    }

    // 使用wxContext中的openid
    return await processLogin(openid, unionid, userInfo)

  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败，请重试',
      error: error.message,
      stack: error.stack
    }
  }
}

// 处理登录逻辑
async function processLogin(openid, unionid, userInfo) {
  try {
    console.log('开始处理登录', { openid, unionid, userInfo })

    // 验证用户信息
    if (!userInfo || !userInfo.nickName) {
      console.log('用户信息不完整，使用默认信息')
      userInfo = {
        nickName: '微信用户',
        avatarUrl: '/images/default-avatar.png',
        ...userInfo
      }
    }

    // 查询用户是否已存在
    console.log('查询用户信息', { openid })
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()

    console.log('用户查询结果', { count: userQuery.data.length })

    let userData = {
      openid: openid,
      unionid: unionid,
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      lastLoginTime: new Date(),
      subscription: {
        type: 'free',
        remainingQuota: 10,
        expiryDate: null
      }
    }

    if (userQuery.data.length === 0) {
      // 新用户，创建用户记录
      console.log('创建新用户', userData)
      userData.createTime = new Date()
      userData.totalUsage = 0

      const addResult = await db.collection('users').add({
        data: userData
      })
      console.log('新用户创建结果', addResult)
    } else {
      // 老用户，更新登录时间和用户信息
      console.log('更新老用户信息')
      const updateResult = await db.collection('users').where({
        openid: openid
      }).update({
        data: {
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
          lastLoginTime: new Date()
        }
      })
      console.log('用户信息更新结果', updateResult)

      // 获取用户的订阅信息
      const existingUser = userQuery.data[0]
      userData.subscription = existingUser.subscription || userData.subscription
      userData.totalUsage = existingUser.totalUsage || 0
    }

    const result = {
      success: true,
      openid: openid,
      userInfo: {
        nickName: userData.nickName,
        avatarUrl: userData.avatarUrl,
        openid: openid
      },
      subscription: userData.subscription
    }

    console.log('登录成功，返回结果', result)
    return result

  } catch (error) {
    console.error('处理登录失败:', error)
    return {
      success: false,
      message: '登录处理失败，请重试',
      error: error.message
    }
  }
}
