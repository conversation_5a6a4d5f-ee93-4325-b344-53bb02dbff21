# IVD智能顾问 API 文档

## 概述

本文档描述了IVD智能顾问小程序的云函数API接口，包括用户认证、AI聊天、支付处理等核心功能。

## 认证机制

所有API调用都基于微信小程序的云开发环境，通过微信登录获取用户身份信息。

```javascript
// 获取用户上下文
const wxContext = cloud.getWXContext()
const { openid, unionid } = wxContext
```

## API 接口列表

### 1. 用户登录 - login

#### 接口描述
处理用户微信登录，创建或更新用户信息。

#### 请求参数
```javascript
{
  "code": "string",        // 微信登录code
  "userInfo": {
    "nickName": "string",  // 用户昵称
    "avatarUrl": "string"  // 用户头像URL
  }
}
```

#### 返回结果
```javascript
{
  "success": true,
  "openid": "string",      // 用户openid
  "userInfo": {
    "nickName": "string",
    "avatarUrl": "string", 
    "openid": "string"
  },
  "subscription": {
    "type": "free",        // 订阅类型: free/basic/premium
    "remainingQuota": 10,  // 剩余使用次数
    "expiryDate": null     // 到期时间
  }
}
```

#### 错误响应
```javascript
{
  "success": false,
  "message": "错误信息"
}
```

#### 使用示例
```javascript
wx.cloud.callFunction({
  name: 'login',
  data: {
    code: res.code,
    userInfo: userInfo
  },
  success: (res) => {
    console.log('登录成功', res.result)
  }
})
```

---

### 2. AI聊天 - aiChat

#### 接口描述
处理用户与AI的对话请求，支持多种AI模型。

#### 请求参数
```javascript
{
  "message": "string",     // 用户消息内容
  "model": "string",       // AI模型ID (可选，默认deepseek-v3-0324)
  "context": [             // 对话上下文 (可选)
    {
      "role": "user",
      "content": "string"
    },
    {
      "role": "assistant", 
      "content": "string"
    }
  ]
}
```

#### 支持的AI模型
| 模型ID | 模型名称 | 订阅要求 | 描述 |
|--------|----------|----------|------|
| deepseek-v3-0324 | DeepSeek-V3 | 免费版+ | 基础对话模型 |
| deepseek-r1-0528 | DeepSeek-R1 | 基础版+ | 逻辑推理专家 |
| qwen3 | Qwen3 | 基础版+ | 中文理解优秀 |
| qwen-max | Qwen Max | 高级版 | 通义千问高级版 |
| chatgpt | ChatGPT | 高级版 | OpenAI通用模型 |

#### 返回结果
```javascript
{
  "success": true,
  "reply": "string",           // AI回复内容
  "model": "string",           // 使用的模型ID
  "remainingQuota": 9          // 剩余使用次数
}
```

#### 错误响应
```javascript
{
  "success": false,
  "message": "错误信息"        // 如：使用额度不足、模型不可用等
}
```

#### 使用示例
```javascript
wx.cloud.callFunction({
  name: 'aiChat',
  data: {
    message: '如何进行IVD产品的研发规划？',
    model: 'deepseek-v3-0324',
    context: [
      { role: 'user', content: '你好' },
      { role: 'assistant', content: '您好！我是IVD智能顾问...' }
    ]
  },
  success: (res) => {
    console.log('AI回复', res.result.reply)
  }
})
```

---

### 3. 创建支付订单 - createPayment

#### 接口描述
创建微信支付订单，用于用户购买订阅套餐。

#### 请求参数
```javascript
{
  "planId": "string",      // 套餐ID: basic/premium
  "amount": 2900,          // 支付金额(分)
  "description": "string"  // 订单描述
}
```

#### 套餐配置
| 套餐ID | 套餐名称 | 价格(元) | 咨询次数 | 可用模型 |
|--------|----------|----------|----------|----------|
| basic | 基础版 | 29 | 100次/月 | DeepSeek系列 + Qwen3 |
| premium | 高级版 | 99 | 500次/月 | 所有模型 |

#### 返回结果
```javascript
{
  "success": true,
  "payment": {
    "timeStamp": "string",
    "nonceStr": "string", 
    "package": "string",
    "signType": "string",
    "paySign": "string"
  },
  "orderNo": "string"      // 订单号
}
```

#### 使用示例
```javascript
wx.cloud.callFunction({
  name: 'createPayment',
  data: {
    planId: 'basic',
    amount: 2900,
    description: '基础版 - 月套餐'
  },
  success: (res) => {
    if (res.result.success) {
      // 调用微信支付
      wx.requestPayment({
        ...res.result.payment,
        success: () => {
          console.log('支付成功')
        }
      })
    }
  }
})
```

---

### 4. 支付回调 - payCallback

#### 接口描述
处理微信支付回调，更新订单状态和用户订阅信息。

#### 回调参数
```javascript
{
  "orderNo": "string",     // 订单号
  "resultCode": "string",  // 支付结果: SUCCESS/FAIL
  "openid": "string"       // 用户openid
}
```

#### 处理逻辑
1. 验证订单存在性
2. 更新订单状态
3. 更新用户订阅信息
4. 记录支付日志

#### 返回结果
```javascript
{
  "success": true
}
```

---

## 数据模型

### User 用户模型
```javascript
{
  "_id": "ObjectId",
  "openid": "string",
  "unionid": "string", 
  "nickName": "string",
  "avatarUrl": "string",
  "subscription": {
    "type": "free|basic|premium",
    "remainingQuota": "number",
    "expiryDate": "string|null"
  },
  "createTime": "Date",
  "lastLoginTime": "Date",
  "totalUsage": "number"
}
```

### ChatHistory 聊天记录模型
```javascript
{
  "_id": "ObjectId",
  "openid": "string",
  "userMessage": "string",
  "aiReply": "string", 
  "model": "string",
  "timestamp": "Date"
}
```

### Order 订单模型
```javascript
{
  "_id": "ObjectId",
  "orderNo": "string",
  "openid": "string",
  "planId": "string",
  "amount": "number",
  "description": "string",
  "status": "pending|paid|failed",
  "createTime": "Date",
  "payTime": "Date"
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 用户未登录 | 需要先调用login接口 |
| 1002 | 使用额度不足 | 需要升级订阅套餐 |
| 1003 | 模型不可用 | 当前订阅不支持该模型 |
| 1004 | AI服务异常 | AI API调用失败 |
| 2001 | 订单不存在 | 支付回调中订单号无效 |
| 2002 | 支付失败 | 微信支付处理失败 |
| 9999 | 系统错误 | 服务器内部错误 |

## 限流规则

### AI聊天接口
- 免费用户：每分钟最多5次请求
- 付费用户：每分钟最多20次请求
- 单次消息最大长度：500字符

### 支付接口
- 每用户每分钟最多3次创建订单请求
- 重复订单检测：5分钟内相同金额订单

## 安全措施

1. **身份验证**: 基于微信openid的用户身份验证
2. **权限控制**: 数据库安全规则限制数据访问
3. **API密钥**: 环境变量存储，云函数内部调用
4. **支付安全**: 微信官方支付SDK，签名验证
5. **内容过滤**: 用户输入内容安全检查

## 监控指标

### 性能指标
- API响应时间
- 云函数执行时长
- 数据库查询性能
- AI API调用延迟

### 业务指标
- 用户活跃度
- 消息发送量
- 订阅转化率
- 支付成功率

## 版本更新

### v2.0.0 (当前版本)
- 支持多AI模型切换
- 新增分级订阅功能
- 优化支付流程
- 改进用户界面

### 后续规划
- 增加更多AI模型
- 支持语音对话
- 添加文件上传功能
- 企业版定制服务

---

如有疑问或需要技术支持，请联系开发团队。
