// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: null,
    isLoggedIn: false,
    subscription: {
      type: 'free',
      remainingQuota: 10,
      expiryDate: null
    },
    menuItems: [
      {
        icon: '💳',
        title: '我的订阅',
        desc: '查看和管理订阅套餐',
        url: '/pages/subscription/subscription'
      },
      {
        icon: '💬',
        title: '聊天记录',
        desc: '查看历史对话记录',
        url: '/pages/history/history'
      },
      {
        icon: '⚙️',
        title: '设置',
        desc: '个人设置和偏好',
        url: '/pages/settings/settings'
      },
      {
        icon: '❓',
        title: '帮助与反馈',
        desc: '使用帮助和问题反馈',
        url: '/pages/help/help'
      },
      {
        icon: '📞',
        title: '联系我们',
        desc: '获取技术支持',
        url: '/pages/contact/contact'
      }
    ]
  },

  onLoad() {
    this.loadUserData()
  },

  onShow() {
    this.loadUserData()
  },

  // 加载用户数据
  loadUserData() {
    const userInfo = app.globalData.userInfo
    const isLoggedIn = app.globalData.isLoggedIn
    const subscription = app.globalData.subscription

    this.setData({
      userInfo,
      isLoggedIn,
      subscription
    })
  },

  // 登录
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储
          wx.removeStorageSync('userInfo')
          wx.removeStorageSync('openid')
          
          // 重置全局数据
          app.globalData.userInfo = null
          app.globalData.isLoggedIn = false
          app.globalData.subscription = {
            type: 'free',
            remainingQuota: 10,
            expiryDate: null
          }
          
          // 更新页面数据
          this.setData({
            userInfo: null,
            isLoggedIn: false,
            subscription: {
              type: 'free',
              remainingQuota: 10,
              expiryDate: null
            }
          })
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          })
        }
      }
    })
  },

  // 菜单项点击
  onMenuItemTap(e) {
    const index = e.currentTarget.dataset.index
    const item = this.data.menuItems[index]
    
    // 检查是否需要登录
    if (!this.data.isLoggedIn && ['subscription', 'history', 'settings'].some(key => item.url.includes(key))) {
      wx.showModal({
        title: '提示',
        content: '请先登录后使用此功能',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            this.goToLogin()
          }
        }
      })
      return
    }
    
    // 检查页面是否存在
    if (item.url.includes('history') || item.url.includes('settings') || 
        item.url.includes('help') || item.url.includes('contact')) {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
      return
    }
    
    wx.navigateTo({
      url: item.url
    })
  },

  // 编辑个人信息
  editProfile() {
    if (!this.data.isLoggedIn) {
      this.goToLogin()
      return
    }

    // 显示操作选项
    wx.showActionSheet({
      itemList: ['更新头像和昵称', '仅更新昵称'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 更新头像和昵称
          this.updateUserProfile()
        } else if (res.tapIndex === 1) {
          // 仅更新昵称
          this.updateNickName()
        }
      }
    })
  },

  // 更新用户资料（头像+昵称）
  updateUserProfile() {
    console.log('开始更新用户资料')

    if (wx.getUserProfile) {
      wx.getUserProfile({
        desc: '更新您的头像和昵称',
        success: (res) => {
          console.log('获取用户信息成功', res)
          this.callUpdateUserInfo(res.userInfo.nickName, res.userInfo.avatarUrl)
        },
        fail: (err) => {
          console.error('获取用户信息失败', err)
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: '当前环境不支持此功能',
        icon: 'none'
      })
    }
  },

  // 更新昵称
  updateNickName() {
    wx.showModal({
      title: '更新昵称',
      content: '请输入新的昵称',
      editable: true,
      placeholderText: this.data.userInfo?.nickName || '请输入昵称',
      success: (res) => {
        if (res.confirm && res.content.trim()) {
          this.callUpdateUserInfo(res.content.trim(), null)
        }
      }
    })
  },

  // 调用更新用户信息云函数
  callUpdateUserInfo(nickName, avatarUrl) {
    wx.showLoading({
      title: '更新中...'
    })

    const updateData = {}
    if (nickName) updateData.nickName = nickName
    if (avatarUrl) updateData.avatarUrl = avatarUrl

    wx.cloud.callFunction({
      name: 'updateUserInfo',
      data: updateData,
      success: (res) => {
        wx.hideLoading()
        console.log('更新用户信息响应', res)

        if (res.result && res.result.success) {
          // 更新本地存储和全局数据
          const newUserInfo = res.result.userInfo
          wx.setStorageSync('userInfo', newUserInfo)
          app.globalData.userInfo = newUserInfo

          // 更新页面数据
          this.setData({
            userInfo: newUserInfo
          })

          wx.showToast({
            title: '更新成功',
            icon: 'success'
          })
        } else {
          wx.showToast({
            title: res.result?.message || '更新失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('更新用户信息失败', err)
        wx.showToast({
          title: '更新失败，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 查看订阅详情
  viewSubscription() {
    if (!this.data.isLoggedIn) {
      this.goToLogin()
      return
    }
    
    wx.navigateTo({
      url: '/pages/subscription/subscription'
    })
  },

  // 分享小程序
  onShareAppMessage() {
    return {
      title: 'IVD智能顾问 - 您的专属研发、注册、销售顾问',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'IVD智能顾问 - 专业AI咨询服务',
      imageUrl: '/images/share-cover.jpg'
    }
  }
})
