/* pages/profile/profile.wxss */

/* 用户信息区域 */
.user-section {
  margin-bottom: 30rpx;
}

.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  padding: 40rpx 30rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-status {
  font-size: 26rpx;
  opacity: 0.8;
}

.edit-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.edit-btn::after {
  border: none;
}

.edit-icon {
  font-size: 28rpx;
  color: #ffffff;
}

/* 订阅状态 */
.subscription-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.status-type {
  font-size: 30rpx;
  font-weight: 600;
}

.status-quota {
  text-align: center;
  margin: 0 30rpx;
}

.quota-label {
  font-size: 22rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
}

.quota-value {
  font-size: 32rpx;
  font-weight: 700;
}

.status-arrow {
  font-size: 28rpx;
  opacity: 0.8;
}

/* 未登录提示 */
.login-prompt {
  text-align: center;
  padding: 60rpx 40rpx;
}

.prompt-content {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  gap: 20rpx;
}

.prompt-avatar {
  font-size: 60rpx;
  width: 100rpx;
  height: 100rpx;
  background: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.prompt-text {
  text-align: left;
}

.prompt-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.prompt-desc {
  font-size: 26rpx;
  color: #666666;
}

.login-btn {
  width: 60%;
}

/* 功能菜单 */
.menu-section {
  margin-bottom: 30rpx;
}

.menu-list {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f0f7ff;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 24rpx;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #666666;
}

.menu-arrow {
  font-size: 28rpx;
  color: #cccccc;
}

/* 应用信息 */
.app-info-section {
  margin-bottom: 30rpx;
}

.info-card {
  text-align: center;
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  gap: 20rpx;
}

.app-logo {
  font-size: 60rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-details {
  text-align: left;
}

.app-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.app-version {
  font-size: 24rpx;
  color: #666666;
}

.app-description {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30rpx;
  text-align: left;
}

.share-actions {
  display: flex;
  justify-content: center;
}

.share-btn {
  background: #f0f7ff;
  color: #1976D2;
  border: 1rpx solid #1976D2;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.share-btn::after {
  border: none;
}

.share-icon {
  font-size: 24rpx;
}

/* 退出登录 */
.logout-section {
  margin-bottom: 30rpx;
}

.logout-btn {
  width: 100%;
  background: #ffffff;
  color: #f44336;
  border: 2rpx solid #f44336;
  border-radius: 50rpx;
  padding: 24rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.logout-btn::after {
  border: none;
}

/* 底部版权 */
.footer {
  padding: 40rpx 0;
  text-align: center;
}

.copyright {
  opacity: 0.6;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.copyright-desc {
  display: block;
  font-size: 22rpx;
  color: #999999;
}
