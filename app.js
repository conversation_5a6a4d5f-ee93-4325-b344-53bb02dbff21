// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      wx.cloud.init({
        env: 'cloudbase-7g8nxwah43c62b19', // 云开发环境ID
        traceUser: true,
      })
    }

    // 检查登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 小程序显示时的处理
  },

  onHide() {
    // 小程序隐藏时的处理
  },

  onError(msg) {
    console.error('小程序错误:', msg)
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    currentAIModel: 'deepseek-v3-0324', // 默认AI模型
    subscription: {
      type: 'free', // free, basic, premium
      remainingQuota: 10, // 剩余使用次数
      expiryDate: null
    }
  }
})
