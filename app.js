// app.js
const ErrorMonitor = require('./utils/errorMonitor.js')

App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      try {
        wx.cloud.init({
          env: 'cloudbase-7g8nxwah43c62b19', // 云开发环境ID
          traceUser: true,
        })
        console.log('✅ 云开发初始化成功')
      } catch (error) {
        console.error('❌ 云开发初始化失败:', error)
      }
    }

    // 检查登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 小程序显示时的处理
  },

  onHide() {
    // 小程序隐藏时的处理
  },

  onError(msg) {
    // 过滤掉已知的内部错误，避免干扰开发
    const ignoredErrors = [
      'wxfile://',
      'miniprogramlop',
      'log2',
      'backgroundfetch privacy fail',
      'private_getBackgroundFetchData:fail',
      'no such file or directory'
    ]

    // 检查是否为需要忽略的错误
    const shouldIgnore = ignoredErrors.some(pattern =>
      msg && msg.toString().includes(pattern)
    )

    if (shouldIgnore) {
      console.warn('⚠️ 已忽略内部错误:', msg)
      return false // 阻止错误继续传播
    }

    // 记录真正需要关注的错误
    console.error('❌ 小程序错误:', msg)
    return true
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    currentAIModel: 'deepseek-v3-0324', // 默认AI模型
    subscription: {
      type: 'free', // free, basic, premium
      remainingQuota: 10, // 剩余使用次数
      expiryDate: null
    }
  }
})
