// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    } else {
      try {
        wx.cloud.init({
          env: 'cloudbase-7g8nxwah43c62b19', // 云开发环境ID
          traceUser: true,
        })
        console.log('✅ 云开发初始化成功')
      } catch (error) {
        console.error('❌ 云开发初始化失败:', error)
      }
    }

    // 检查登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 小程序显示时的处理
  },

  onHide() {
    // 小程序隐藏时的处理
  },

  onError(msg) {
    console.error('小程序错误:', msg)

    // 过滤掉 wxfile 相关的内部错误，这些通常是SDK内部错误，不影响实际功能
    if (msg && (msg.includes('wxfile://') || msg.includes('miniprogramlop') || msg.includes('log2'))) {
      console.warn('⚠️ 检测到SDK内部文件访问错误，已忽略:', msg)
      return false // 阻止错误继续传播
    }

    // 其他错误正常处理
    return true
  },

  // 检查登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
    }
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    currentAIModel: 'deepseek-v3-0324', // 默认AI模型
    subscription: {
      type: 'free', // free, basic, premium
      remainingQuota: 10, // 剩余使用次数
      expiryDate: null
    }
  }
})
