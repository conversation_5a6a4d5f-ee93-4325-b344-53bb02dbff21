// 云函数：AI聊天
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// AI模型配置
const AI_MODELS = {
  'deepseek-v3-0324': {
    name: 'DeepSeek-V3',
    apiUrl: 'https://api.deepseek.com/v1/chat/completions',
    apiKey: process.env.DEEPSEEK_API_KEY,
    model: 'deepseek-chat'
  },
  'deepseek-r1-0528': {
    name: 'DeepSeek-R1',
    apiUrl: 'https://api.deepseek.com/v1/chat/completions',
    apiKey: process.env.DEEPSEEK_API_KEY,
    model: 'deepseek-reasoner'
  },
  'qwen3': {
    name: 'Qwen3',
    apiUrl: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    apiKey: process.env.QWEN_API_KEY,
    model: 'qwen-turbo'
  },
  'qwen-max': {
    name: 'Qwen <PERSON>',
    apiUrl: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    apiKey: process.env.QWEN_API_KEY,
    model: 'qwen-max'
  },
  'chatgpt': {
    name: 'ChatGPT',
    apiUrl: 'https://api.openai.com/v1/chat/completions',
    apiKey: process.env.OPENAI_API_KEY,
    model: 'gpt-3.5-turbo'
  }
}

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { openid } = wxContext
  
  try {
    const { message, model = 'deepseek-v3-0324', context: chatContext = [] } = event
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录'
      }
    }
    
    // 检查用户订阅和额度
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      }
    }
    
    const user = userQuery.data[0]
    const subscription = user.subscription || { type: 'free', remainingQuota: 0 }
    
    if (subscription.remainingQuota <= 0) {
      return {
        success: false,
        message: '使用额度不足，请升级套餐'
      }
    }
    
    // 检查模型权限
    if (!checkModelPermission(model, subscription.type)) {
      return {
        success: false,
        message: '当前套餐不支持此AI模型，请升级套餐'
      }
    }
    
    // 调用AI API
    const aiResponse = await callAIAPI(model, message, chatContext)
    
    if (!aiResponse.success) {
      return {
        success: false,
        message: aiResponse.message
      }
    }
    
    // 更新用户使用额度
    await db.collection('users').where({
      openid: openid
    }).update({
      data: {
        'subscription.remainingQuota': subscription.remainingQuota - 1,
        totalUsage: (user.totalUsage || 0) + 1,
        lastUsageTime: new Date()
      }
    })
    
    // 保存聊天记录
    await db.collection('chatHistory').add({
      data: {
        openid: openid,
        userMessage: message,
        aiReply: aiResponse.reply,
        model: model,
        timestamp: new Date()
      }
    })
    
    return {
      success: true,
      reply: aiResponse.reply,
      model: model,
      remainingQuota: subscription.remainingQuota - 1
    }
    
  } catch (error) {
    console.error('AI聊天失败:', error)
    return {
      success: false,
      message: 'AI服务暂时不可用，请稍后重试'
    }
  }
}

// 检查模型权限
function checkModelPermission(model, subscriptionType) {
  const freeModels = ['deepseek-v3-0324']
  const basicModels = ['deepseek-v3-0324', 'deepseek-r1-0528', 'qwen3']
  const premiumModels = Object.keys(AI_MODELS)
  
  switch (subscriptionType) {
    case 'free':
      return freeModels.includes(model)
    case 'basic':
      return basicModels.includes(model)
    case 'premium':
      return premiumModels.includes(model)
    default:
      return false
  }
}

// 调用AI API
async function callAIAPI(model, message, context) {
  const modelConfig = AI_MODELS[model]
  
  if (!modelConfig || !modelConfig.apiKey) {
    return {
      success: false,
      message: 'AI模型配置错误'
    }
  }
  
  try {
    // 构建系统提示词
    const systemPrompt = `你是IVD智能顾问，专注于为用户提供IVD（体外诊断）行业的专业咨询服务。你的专业领域包括：

1. 产品研发：IVD产品的研发流程、技术要求、质量控制等
2. 注册申报：医疗器械注册申报流程、法规要求、资料准备等  
3. 市场销售：IVD产品的市场策略、销售渠道、竞争分析等

请以专业、友好的语气回答用户问题，提供准确、实用的建议。如果问题超出IVD领域，请礼貌地引导用户回到相关话题。`
    
    // 构建消息数组
    const messages = [
      { role: 'system', content: systemPrompt },
      ...context,
      { role: 'user', content: message }
    ]
    
    let response
    
    if (model.startsWith('deepseek') || model === 'chatgpt') {
      // OpenAI格式API
      response = await fetch(modelConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${modelConfig.apiKey}`
        },
        body: JSON.stringify({
          model: modelConfig.model,
          messages: messages,
          max_tokens: 1000,
          temperature: 0.7
        })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error?.message || '调用AI API失败')
      }
      
      return {
        success: true,
        reply: data.choices[0].message.content
      }
      
    } else if (model.startsWith('qwen')) {
      // 通义千问API格式
      response = await fetch(modelConfig.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${modelConfig.apiKey}`
        },
        body: JSON.stringify({
          model: modelConfig.model,
          input: {
            messages: messages
          },
          parameters: {
            max_tokens: 1000,
            temperature: 0.7
          }
        })
      })
      
      const data = await response.json()
      
      if (data.code !== '200') {
        throw new Error(data.message || '调用AI API失败')
      }
      
      return {
        success: true,
        reply: data.output.choices[0].message.content
      }
    }
    
  } catch (error) {
    console.error('调用AI API失败:', error)
    return {
      success: false,
      message: error.message || 'AI服务调用失败'
    }
  }
}
