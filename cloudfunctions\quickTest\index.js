// 快速测试云函数：验证 wxContext 属性
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

exports.main = async (event, context) => {
  try {
    console.log('快速测试开始')
    
    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    console.log('完整的 wxContext:', wxContext)
    
    // 测试不同的属性获取方式
    const test1 = {
      method: '小写解构',
      openid: wxContext.openid,
      unionid: wxContext.unionid,
      appid: wxContext.appid
    }
    
    const test2 = {
      method: '大写直接访问',
      openid: wxContext.OPENID,
      unionid: wxContext.UNIONID,
      appid: wxContext.APPID
    }
    
    const test3 = {
      method: '解构大写',
      ...(() => {
        const { OPENID, UNIONID, APPID } = wxContext
        return { openid: OPENID, unionid: UNIONID, appid: APPID }
      })()
    }
    
    console.log('测试结果1:', test1)
    console.log('测试结果2:', test2)
    console.log('测试结果3:', test3)
    
    return {
      success: true,
      message: '测试完成',
      wxContext,
      tests: [test1, test2, test3],
      recommendation: test2.openid ? '使用大写直接访问' : '需要进一步检查'
    }
    
  } catch (error) {
    console.error('测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
