<!--pages/subscription/subscription.wxml-->
<view class="container">
  <!-- 当前订阅状态 -->
  <view class="current-subscription">
    <view class="status-card card">
      <view class="status-header">
        <view class="status-title">当前套餐</view>
        <view class="status-badge {{currentSubscription.type}}">
          {{currentSubscriptionName}}
        </view>
      </view>
      
      <view class="status-details">
        <view class="detail-item">
          <view class="detail-label">剩余次数</view>
          <view class="detail-value">{{currentSubscription.remainingQuota}}次</view>
        </view>
        <view class="detail-item" wx:if="{{currentSubscription.expiryDate}}">
          <view class="detail-label">到期时间</view>
          <view class="detail-value">{{currentSubscription.expiryDate}}</view>
        </view>
        <view class="detail-item" wx:if="{{!currentSubscription.expiryDate}}">
          <view class="detail-label">套餐类型</view>
          <view class="detail-value">永久有效</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 套餐选择 -->
  <view class="plans-section">
    <view class="section-title">
      <text class="title-text">选择套餐</text>
      <text class="title-desc">升级享受更多功能</text>
    </view>
    
    <view class="plans-grid">
      <view 
        class="plan-card {{selectedPlan && selectedPlan.id === item.id ? 'selected' : ''}}"
        wx:for="{{plans}}"
        wx:key="id"
        bindtap="selectPlan"
        data-plan="{{item.id}}"
      >
        <!-- 推荐标签 -->
        <view class="recommend-badge" wx:if="{{item.recommended}}">推荐</view>
        
        <view class="plan-header">
          <view class="plan-name">{{item.name}}</view>
          <view class="plan-price">
            <text class="price-symbol" wx:if="{{item.price > 0}}">¥</text>
            <text class="price-amount" wx:if="{{item.price === 0}}">免费</text>
            <text class="price-amount" wx:if="{{item.price > 0}}">{{item.price}}</text>
            <text class="price-period" wx:if="{{item.price > 0}}">/ {{item.period}}</text>
          </view>
        </view>
        
        <view class="plan-quota">
          每月 {{item.quota}} 次AI咨询
        </view>
        
        <view class="plan-features">
          <view 
            class="feature-item"
            wx:for="{{item.features}}"
            wx:key="index"
            wx:for-item="feature"
          >
            <text class="feature-icon">✓</text>
            <text class="feature-text">{{feature}}</text>
          </view>
        </view>
        
        <view class="plan-action">
          <button 
            class="plan-btn {{currentSubscription.type === item.id ? 'current' : ''}}"
            bindtap="viewPlanDetails"
            data-plan="{{item.id}}"
            catchtap="viewPlanDetails"
          >
            <text wx:if="{{currentSubscription.type === item.id}}">当前套餐</text>
            <text wx:if="{{currentSubscription.type !== item.id}}">查看详情</text>
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 购买按钮 -->
  <view class="purchase-section" wx:if="{{selectedPlan}}">
    <view class="purchase-card card">
      <view class="selected-plan">
        <view class="selected-title">已选择套餐</view>
        <view class="selected-info">
          <text class="selected-name">{{selectedPlan.name}}</text>
          <text class="selected-price">{{selectedPlanPrice}}</text>
        </view>
      </view>
      
      <button 
        class="purchase-btn btn-primary"
        bindtap="purchasePlan"
        disabled="{{isLoading}}"
      >
        <loading wx:if="{{isLoading}}" size="20px" color="#ffffff"></loading>
        <text wx:if="{{!isLoading && selectedPlan.price === 0}}">激活免费套餐</text>
        <text wx:if="{{!isLoading && selectedPlan.price > 0}}">立即购买</text>
        <text wx:if="{{isLoading}}">处理中...</text>
      </button>
    </view>
  </view>

  <!-- 客服联系 -->
  <view class="support-section">
    <view class="support-card card">
      <view class="support-content">
        <view class="support-icon">💬</view>
        <view class="support-text">
          <view class="support-title">需要帮助？</view>
          <view class="support-desc">联系我们的专业客服团队</view>
        </view>
      </view>
      <button class="support-btn btn-outline" bindtap="contactSupport">
        联系客服
      </button>
    </view>
  </view>
</view>
