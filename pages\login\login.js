// pages/login/login.js
const app = getApp()

Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    isLoading: false
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.isLoggedIn) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 微信登录
  onWechatLogin(e) {
    this.setData({ isLoading: true })

    // 先调用微信登录获取code
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取用户信息
          this.getUserProfile(res.code)
        } else {
          console.error('登录失败！' + res.errMsg)
          this.setData({ isLoading: false })
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        this.setData({ isLoading: false })
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 获取用户信息
  getUserProfile(code) {
    console.log('开始获取用户信息，code:', code)

    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功', res)
          this.cloudLogin(code, res.userInfo)
        },
        fail: (err) => {
          console.error('获取用户信息失败', err)
          // 用户拒绝授权，使用默认信息
          console.log('用户拒绝授权，使用默认信息')
          this.cloudLogin(code, {
            nickName: '微信用户',
            avatarUrl: '/images/default-avatar.png'
          })
        }
      })
    } else {
      // 兼容旧版本，使用默认信息
      console.log('不支持getUserProfile，使用默认用户信息')
      this.cloudLogin(code, {
        nickName: '微信用户',
        avatarUrl: '/images/default-avatar.png'
      })
    }
  },

  // 云函数登录
  cloudLogin(code, userInfo) {
    console.log('开始调用登录云函数', { code, userInfo })

    wx.cloud.callFunction({
      name: 'login',
      data: {
        code: code,
        userInfo: userInfo
      },
      success: (res) => {
        console.log('云函数登录响应', res)

        if (res.result && res.result.success) {
          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', res.result.userInfo)
          wx.setStorageSync('openid', res.result.openid)

          // 更新全局数据
          app.globalData.userInfo = res.result.userInfo
          app.globalData.isLoggedIn = true

          // 保存订阅信息
          if (res.result.subscription) {
            app.globalData.subscription = res.result.subscription
          }

          this.setData({ isLoading: false })

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })

          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }, 1500)
        } else {
          console.error('登录失败', res.result)
          this.setData({ isLoading: false })
          wx.showToast({
            title: res.result?.message || '登录失败，请重试',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('云函数调用失败', err)
        this.setData({ isLoading: false })

        let errorMsg = '登录失败，请重试'
        if (err.errMsg) {
          if (err.errMsg.includes('cloud function execution fail')) {
            errorMsg = '服务器繁忙，请稍后重试'
          } else if (err.errMsg.includes('network')) {
            errorMsg = '网络连接失败，请检查网络'
          }
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    })
  },

  // 快速体验（游客模式）
  onGuestMode() {
    // 设置游客模式
    const guestInfo = {
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.png',
      isGuest: true
    }
    
    wx.setStorageSync('userInfo', guestInfo)
    app.globalData.userInfo = guestInfo
    app.globalData.isLoggedIn = true
    
    wx.showToast({
      title: '进入游客模式',
      icon: 'success'
    })
    
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }, 1000)
  },

  // 返回首页
  goBack() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
