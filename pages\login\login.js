// pages/login/login.js
const app = getApp()

Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    isLoading: false
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.isLoggedIn) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 微信登录
  onWechatLogin(e) {
    this.setData({ isLoading: true })

    // 先调用微信登录获取code
    wx.login({
      success: (res) => {
        if (res.code) {
          // 获取用户信息
          this.getUserProfile(res.code)
        } else {
          console.error('登录失败！' + res.errMsg)
          this.setData({ isLoading: false })
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        this.setData({ isLoading: false })
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 获取用户信息
  getUserProfile(code) {
    console.log('开始获取用户信息，code:', code)

    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      console.log('支持getUserProfile，尝试获取真实用户信息')
      wx.getUserProfile({
        desc: '用于完善会员资料，提供更好的服务体验',
        success: (res) => {
          console.log('✅ 获取真实用户信息成功', res)
          console.log('用户昵称:', res.userInfo.nickName)
          console.log('用户头像:', res.userInfo.avatarUrl)
          this.cloudLogin(code, res.userInfo)
        },
        fail: (err) => {
          console.error('❌ 获取用户信息失败', err)
          console.log('用户可能拒绝了授权，使用默认信息继续登录')

          // 显示提示，让用户知道可以稍后在设置中更新信息
          wx.showModal({
            title: '提示',
            content: '您可以稍后在个人中心更新头像和昵称',
            showCancel: false,
            confirmText: '知道了'
          })

          this.cloudLogin(code, {
            nickName: '微信用户',
            avatarUrl: '/images/default-avatar.png'
          })
        }
      })
    } else {
      // 兼容旧版本或不支持的情况
      console.log('⚠️ 不支持getUserProfile，使用默认用户信息')
      console.log('可能的原因：基础库版本过低或在开发者工具中')

      // 尝试使用 getUserInfo（已废弃但可能在某些情况下仍可用）
      if (wx.getUserInfo) {
        console.log('尝试使用getUserInfo获取用户信息')
        wx.getUserInfo({
          success: (res) => {
            console.log('✅ getUserInfo成功', res)
            this.cloudLogin(code, res.userInfo)
          },
          fail: (err) => {
            console.log('❌ getUserInfo失败', err)
            this.cloudLogin(code, {
              nickName: '微信用户',
              avatarUrl: '/images/default-avatar.png'
            })
          }
        })
      } else {
        this.cloudLogin(code, {
          nickName: '微信用户',
          avatarUrl: '/images/default-avatar.png'
        })
      }
    }
  },

  // 云函数登录
  cloudLogin(code, userInfo) {
    console.log('开始调用登录云函数', { code, userInfo })

    wx.cloud.callFunction({
      name: 'login',
      data: {
        code: code,
        userInfo: userInfo
      },
      success: (res) => {
        console.log('云函数登录响应', res)

        if (res.result && res.result.success) {
          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', res.result.userInfo)
          wx.setStorageSync('openid', res.result.openid)

          // 更新全局数据
          app.globalData.userInfo = res.result.userInfo
          app.globalData.isLoggedIn = true

          // 保存订阅信息
          if (res.result.subscription) {
            app.globalData.subscription = res.result.subscription
          }

          this.setData({ isLoading: false })

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })

          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }, 1500)
        } else {
          console.error('登录失败', res.result)
          this.setData({ isLoading: false })
          wx.showToast({
            title: res.result?.message || '登录失败，请重试',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('云函数调用失败', err)
        this.setData({ isLoading: false })

        let errorMsg = '登录失败，请重试'
        if (err.errMsg) {
          if (err.errMsg.includes('cloud function execution fail')) {
            errorMsg = '服务器繁忙，请稍后重试'
          } else if (err.errMsg.includes('network')) {
            errorMsg = '网络连接失败，请检查网络'
          }
        }

        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      }
    })
  },

  // 快速体验（游客模式）
  onGuestMode() {
    // 设置游客模式
    const guestInfo = {
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.png',
      isGuest: true
    }
    
    wx.setStorageSync('userInfo', guestInfo)
    app.globalData.userInfo = guestInfo
    app.globalData.isLoggedIn = true
    
    wx.showToast({
      title: '进入游客模式',
      icon: 'success'
    })
    
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }, 1000)
  },

  // 返回首页
  goBack() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
