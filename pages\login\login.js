// pages/login/login.js
const app = getApp()

Page({
  data: {
    canIUse: wx.canIUse('button.open-type.getUserInfo'),
    isLoading: false
  },

  onLoad() {
    // 检查是否已经登录
    if (app.globalData.isLoggedIn) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 微信登录
  onWechatLogin(e) {
    if (e.detail.errMsg !== 'getUserInfo:ok') {
      wx.showToast({
        title: '登录已取消',
        icon: 'none'
      })
      return
    }

    this.setData({ isLoading: true })

    // 获取用户信息
    const userInfo = e.detail.userInfo

    // 调用微信登录
    wx.login({
      success: (res) => {
        if (res.code) {
          // 调用云函数进行登录
          this.cloudLogin(res.code, userInfo)
        } else {
          console.error('登录失败！' + res.errMsg)
          this.setData({ isLoading: false })
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          })
        }
      },
      fail: () => {
        this.setData({ isLoading: false })
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        })
      }
    })
  },

  // 云函数登录
  cloudLogin(code, userInfo) {
    wx.cloud.callFunction({
      name: 'login',
      data: {
        code: code,
        userInfo: userInfo
      },
      success: (res) => {
        console.log('云函数登录成功', res)
        
        if (res.result.success) {
          // 保存用户信息到本地存储
          wx.setStorageSync('userInfo', res.result.userInfo)
          wx.setStorageSync('openid', res.result.openid)
          
          // 更新全局数据
          app.globalData.userInfo = res.result.userInfo
          app.globalData.isLoggedIn = true
          
          this.setData({ isLoading: false })
          
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }, 1500)
        } else {
          this.setData({ isLoading: false })
          wx.showToast({
            title: res.result.message || '登录失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('云函数调用失败', err)
        this.setData({ isLoading: false })
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 快速体验（游客模式）
  onGuestMode() {
    // 设置游客模式
    const guestInfo = {
      nickName: '游客用户',
      avatarUrl: '/images/default-avatar.png',
      isGuest: true
    }
    
    wx.setStorageSync('userInfo', guestInfo)
    app.globalData.userInfo = guestInfo
    app.globalData.isLoggedIn = true
    
    wx.showToast({
      title: '进入游客模式',
      icon: 'success'
    })
    
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }, 1000)
  },

  // 返回首页
  goBack() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  }
})
