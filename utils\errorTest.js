// 错误测试和验证工具
const app = getApp()

const ErrorTest = {
  // 测试云开发初始化
  testCloudInit() {
    console.log('🧪 测试云开发初始化...')
    
    if (!wx.cloud) {
      console.error('❌ 云开发不可用')
      return false
    }
    
    try {
      console.log('✅ 云开发已初始化')
      console.log('环境ID:', 'cloudbase-7g8nxwah43c62b19')
      return true
    } catch (error) {
      console.error('❌ 云开发初始化测试失败:', error)
      return false
    }
  },

  // 测试云函数调用
  async testCloudFunction() {
    console.log('🧪 测试云函数调用...')
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'login',
        data: {
          test: true,
          code: 'test_code',
          userInfo: {
            nickName: '测试用户',
            avatarUrl: 'https://test.avatar.url'
          }
        }
      })
      
      console.log('✅ 云函数调用成功:', result)
      return true
    } catch (error) {
      console.error('❌ 云函数调用失败:', error)
      return false
    }
  },

  // 测试错误处理
  testErrorHandling() {
    console.log('🧪 测试错误处理...')
    
    // 模拟 wxfile 错误
    const wxfileError = 'error occurs:no such file or directory, access \'wxfile://usr/miniprogramlop/log2\''
    
    try {
      // 触发 onError
      app.onError(wxfileError)
      console.log('✅ wxfile 错误已被正确处理')
      return true
    } catch (error) {
      console.error('❌ 错误处理测试失败:', error)
      return false
    }
  },

  // 测试登录状态
  testLoginStatus() {
    console.log('🧪 测试登录状态...')
    
    try {
      const userInfo = app.globalData.userInfo
      const isLoggedIn = app.globalData.isLoggedIn
      
      console.log('用户信息:', userInfo)
      console.log('登录状态:', isLoggedIn)
      
      if (isLoggedIn && userInfo) {
        console.log('✅ 用户已登录')
        return true
      } else {
        console.log('ℹ️ 用户未登录')
        return false
      }
    } catch (error) {
      console.error('❌ 登录状态测试失败:', error)
      return false
    }
  },

  // 综合测试
  async runAllTests() {
    console.log('🚀 开始综合测试...')
    console.log('=====================================')
    
    const results = {
      cloudInit: this.testCloudInit(),
      errorHandling: this.testErrorHandling(),
      loginStatus: this.testLoginStatus(),
      cloudFunction: false
    }
    
    // 异步测试云函数
    try {
      results.cloudFunction = await this.testCloudFunction()
    } catch (error) {
      console.error('云函数测试异常:', error)
    }
    
    console.log('=====================================')
    console.log('📊 测试结果汇总:')
    console.log('云开发初始化:', results.cloudInit ? '✅' : '❌')
    console.log('错误处理:', results.errorHandling ? '✅' : '❌')
    console.log('登录状态:', results.loginStatus ? '✅' : 'ℹ️')
    console.log('云函数调用:', results.cloudFunction ? '✅' : '❌')
    
    const passCount = Object.values(results).filter(Boolean).length
    const totalCount = Object.keys(results).length
    
    console.log(`总体评分: ${passCount}/${totalCount}`)
    
    if (passCount >= 3) {
      console.log('🎉 系统运行正常！')
    } else if (passCount >= 2) {
      console.log('⚠️ 系统基本正常，有部分问题')
    } else {
      console.log('❌ 系统存在较多问题，需要检查')
    }
    
    return results
  },

  // 快速修复建议
  getFixSuggestions(results) {
    const suggestions = []
    
    if (!results.cloudInit) {
      suggestions.push('1. 检查基础库版本是否 >= 2.2.3')
      suggestions.push('2. 确认云开发环境ID正确')
    }
    
    if (!results.cloudFunction) {
      suggestions.push('3. 重新部署云函数')
      suggestions.push('4. 检查云函数权限配置')
    }
    
    if (!results.errorHandling) {
      suggestions.push('5. 检查app.js中的错误处理代码')
    }
    
    if (!results.loginStatus) {
      suggestions.push('6. 尝试重新登录')
    }
    
    if (suggestions.length > 0) {
      console.log('🔧 修复建议:')
      suggestions.forEach(suggestion => console.log(suggestion))
    }
    
    return suggestions
  }
}

// 导出测试工具
module.exports = ErrorTest
