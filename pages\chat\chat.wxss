/* pages/chat/chat.wxss */

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

/* 顶部工具栏 */
.toolbar {
  background: #ffffff;
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #e0e0e0;
  position: relative;
  z-index: 100;
}

.model-selector {
  flex: 1;
}

.current-model {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 24rpx;
  background: #f0f7ff;
  border-radius: 24rpx;
  border: 1rpx solid #1976D2;
}

.model-name {
  font-size: 28rpx;
  color: #1976D2;
  font-weight: 500;
}

.model-icon {
  font-size: 24rpx;
  color: #1976D2;
}

.toolbar-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.action-btn::after {
  border: none;
}

.action-icon {
  font-size: 28rpx;
}

/* 模型选择器下拉 */
.model-dropdown {
  position: absolute;
  top: 100%;
  left: 30rpx;
  right: 30rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  z-index: 99;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.model-dropdown.show {
  max-height: 400rpx;
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.model-option:last-child {
  border-bottom: none;
}

.model-option.unavailable {
  opacity: 0.6;
}

.model-info {
  flex: 1;
}

.model-option .model-name {
  display: block;
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.model-status {
  font-size: 24rpx;
  color: #666666;
}

.model-check {
  color: #1976D2;
  font-size: 32rpx;
  font-weight: bold;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  padding: 20rpx;
}

/* 快速问题 */
.quick-questions {
  margin-bottom: 30rpx;
}

.quick-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
  text-align: center;
}

.questions-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.question-item {
  background: #ffffff;
  padding: 24rpx 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e0e0e0;
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.question-item:active {
  background: #f0f7ff;
  border-color: #1976D2;
}

/* 消息列表 */
.messages-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.message-item.user {
  flex-direction: row-reverse;
}

/* AI消息 */
.message-ai {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  max-width: 85%;
}

.ai-avatar {
  width: 60rpx;
  height: 60rpx;
  background: #1976D2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  flex-shrink: 0;
}

.message-ai .message-content {
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  flex: 1;
}

/* 用户消息 */
.message-user {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  max-width: 85%;
  align-self: flex-end;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  flex-shrink: 0;
}

.message-user .message-content {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  color: #ffffff;
  border-radius: 20rpx 20rpx 8rpx 20rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3);
}

/* 消息内容 */
.message-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.sender-name {
  font-size: 24rpx;
  font-weight: 600;
  color: #1976D2;
}

.message-time {
  font-size: 22rpx;
  color: #999999;
}

.message-user .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.model-tag {
  font-size: 20rpx;
  background: #e3f2fd;
  color: #1976D2;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.message-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
  word-wrap: break-word;
}

.message-user .message-text {
  color: #ffffff;
}

.message-text.error {
  color: #f44336;
}

/* 加载状态 */
.loading-message {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  max-width: 85%;
}

.loading-content {
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 8rpx;
  padding: 20rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: #1976D2;
  border-radius: 50%;
  animation: loading 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-text {
  font-size: 26rpx;
  color: #666666;
}

/* 输入区域 */
.input-area {
  background: #ffffff;
  padding: 20rpx 30rpx 40rpx;
  border-top: 1rpx solid #e0e0e0;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  border: 1rpx solid #e0e0e0;
}

.message-input {
  flex: 1;
  min-height: 60rpx;
  max-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: transparent;
  border: none;
  outline: none;
}

.send-btn {
  width: 60rpx;
  height: 60rpx;
  background: #e0e0e0;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
}

.send-btn.active {
  background: #1976D2;
}

.send-btn::after {
  border: none;
}

.send-icon {
  font-size: 28rpx;
  color: #ffffff;
}

.usage-tip {
  margin-top: 16rpx;
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #999999;
}
