/* pages/subscription/subscription.wxss */

/* 当前订阅状态 */
.current-subscription {
  margin-bottom: 40rpx;
}

.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
}

.status-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-details {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  text-align: center;
  flex: 1;
}

.detail-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.detail-value {
  font-size: 28rpx;
  font-weight: 600;
}

/* 套餐选择 */
.plans-section {
  margin-bottom: 40rpx;
}

.section-title {
  margin-bottom: 30rpx;
  text-align: center;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10rpx;
}

.title-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
}

.plans-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.plan-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  border: 2rpx solid #e0e0e0;
  position: relative;
  transition: all 0.3s ease;
}

.plan-card.selected {
  border-color: #1976D2;
  box-shadow: 0 4rpx 20rpx rgba(25, 118, 210, 0.2);
}

.recommend-badge {
  position: absolute;
  top: -10rpx;
  right: 30rpx;
  background: linear-gradient(135deg, #FF6B6B, #FF8E53);
  color: #ffffff;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.plan-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.plan-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #1976D2;
}

.price-amount {
  font-size: 36rpx;
  font-weight: 700;
  color: #1976D2;
}

.price-period {
  font-size: 24rpx;
  color: #666666;
}

.plan-quota {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 24rpx;
  text-align: center;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 12rpx;
}

.plan-features {
  margin-bottom: 30rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 12rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  color: #4CAF50;
  font-size: 24rpx;
  font-weight: bold;
}

.feature-text {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.plan-action {
  text-align: center;
}

.plan-btn {
  width: 100%;
  background: #f0f7ff;
  color: #1976D2;
  border: 1rpx solid #1976D2;
  border-radius: 50rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

.plan-btn.current {
  background: #e0e0e0;
  color: #666666;
  border-color: #e0e0e0;
}

.plan-btn::after {
  border: none;
}

/* 购买按钮 */
.purchase-section {
  margin-bottom: 40rpx;
}

.purchase-card {
  text-align: center;
}

.selected-plan {
  margin-bottom: 30rpx;
}

.selected-title {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.selected-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
}

.selected-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}

.selected-price {
  font-size: 28rpx;
  color: #1976D2;
  font-weight: 600;
}

.purchase-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

/* 客服联系 */
.support-section {
  margin-bottom: 40rpx;
}

.support-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.support-content {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.support-icon {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  background: #f0f7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.support-text {
  flex: 1;
}

.support-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.support-desc {
  font-size: 24rpx;
  color: #666666;
}

.support-btn {
  padding: 20rpx 30rpx;
  font-size: 26rpx;
}
