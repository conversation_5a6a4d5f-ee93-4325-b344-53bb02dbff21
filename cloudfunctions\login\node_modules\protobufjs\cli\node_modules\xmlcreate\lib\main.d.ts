/**
 * Copyright (C) 2016-2019 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import XmlDocument, { IXmlDocumentOptions } from "./nodes/XmlDocument";
export { default as XmlAttribute, IXmlAttributeOptions } from "./nodes/XmlAttribute";
export { default as XmlAttributeText, IXmlAttributeTextOptions } from "./nodes/XmlAttributeText";
export { default as XmlCdata, IXmlCdataOptions } from "./nodes/XmlCdata";
export { default as XmlCharData, IXmlCharDataOptions } from "./nodes/XmlCharData";
export { default as XmlCharRef, IXmlCharRefOptions } from "./nodes/XmlCharRef";
export { default as XmlComment, IXmlCommentOptions } from "./nodes/XmlComment";
export { default as XmlDecl, IXmlDeclOptions } from "./nodes/XmlDecl";
export { default as XmlDocument, IXmlDocumentOptions } from "./nodes/XmlDocument";
export { default as XmlDtd, IXmlDtdOptions } from "./nodes/XmlDtd";
export { default as XmlDtdAttlist, IXmlDtdAttlistOptions } from "./nodes/XmlDtdAttlist";
export { default as XmlDtdElement, IXmlDtdElementOptions } from "./nodes/XmlDtdElement";
export { default as XmlDtdEntity, IXmlDtdEntityOptions } from "./nodes/XmlDtdEntity";
export { default as XmlDtdNotation, IXmlDtdNotationOptions } from "./nodes/XmlDtdNotation";
export { default as XmlDtdParamEntityRef, IXmlDtdParamEntityRefOptions } from "./nodes/XmlDtdParamEntityRef";
export { default as XmlElement, IXmlElementOptions } from "./nodes/XmlElement";
export { default as XmlEntityRef, IXmlEntityRefOptions } from "./nodes/XmlEntityRef";
export { default as XmlProcInst, IXmlProcInstOptions } from "./nodes/XmlProcInst";
export { IStringOptions } from "./options";
/**
 * Returns a new XML document with the specified options.
 */
export declare function document(options?: IXmlDocumentOptions): XmlDocument;
