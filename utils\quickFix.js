// 快速修复工具 - 可在开发者工具控制台直接运行
const RouteFix = require('./routeFix.js')

const QuickFix = {
  // 一键修复所有常见问题
  async fixAll() {
    console.log('🚀 开始一键修复...')
    console.log('=====================================')
    
    const results = {
      errorFilter: false,
      cloudReinit: false,
      cacheClean: false,
      routeFix: false,
      healthCheck: false
    }
    
    // 1. 启用错误过滤
    try {
      this.enableErrorFilter()
      results.errorFilter = true
      console.log('✅ 错误过滤已启用')
    } catch (e) {
      console.error('❌ 错误过滤启用失败:', e)
    }
    
    // 2. 重新初始化云开发
    try {
      await this.reinitCloud()
      results.cloudReinit = true
      console.log('✅ 云开发重新初始化成功')
    } catch (e) {
      console.error('❌ 云开发重新初始化失败:', e)
    }
    
    // 3. 清理缓存
    try {
      this.cleanCache()
      results.cacheClean = true
      console.log('✅ 缓存清理完成')
    } catch (e) {
      console.error('❌ 缓存清理失败:', e)
    }

    // 4. 修复路由问题
    try {
      await RouteFix.fixRoutes()
      results.routeFix = true
      console.log('✅ 路由修复完成')
    } catch (e) {
      console.error('❌ 路由修复失败:', e)
    }
    
    // 4. 健康检查
    try {
      const health = await this.healthCheck()
      results.healthCheck = health
      console.log('✅ 健康检查完成')
    } catch (e) {
      console.error('❌ 健康检查失败:', e)
    }
    
    console.log('=====================================')
    console.log('🎯 修复结果汇总:')
    console.log('错误过滤:', results.errorFilter ? '✅' : '❌')
    console.log('云开发重初始化:', results.cloudReinit ? '✅' : '❌')
    console.log('缓存清理:', results.cacheClean ? '✅' : '❌')
    console.log('健康检查:', results.healthCheck ? '✅' : '❌')
    
    const successCount = Object.values(results).filter(Boolean).length
    console.log(`总体成功率: ${successCount}/4`)
    
    if (successCount >= 3) {
      console.log('🎉 修复完成！建议重启小程序以确保所有修复生效')
    } else {
      console.log('⚠️ 部分修复失败，请手动检查相关问题')
    }
    
    return results
  },

  // 启用错误过滤
  enableErrorFilter() {
    // 重写全局错误处理
    const originalError = console.error
    console.error = function(...args) {
      const msg = args.join(' ')
      
      // 过滤已知的无害错误
      const ignoredPatterns = [
        'wxfile://',
        'miniprogramlop',
        'log2',
        'backgroundfetch privacy fail',
        'private_getBackgroundFetchData:fail',
        'no such file or directory'
      ]
      
      const shouldIgnore = ignoredPatterns.some(pattern => 
        msg.includes(pattern)
      )
      
      if (shouldIgnore) {
        console.warn('⚠️ [已过滤] 内部错误:', msg)
        return
      }
      
      // 正常错误继续输出
      originalError.apply(console, args)
    }
    
    console.log('🛡️ 错误过滤器已启用')
  },

  // 重新初始化云开发
  async reinitCloud() {
    if (!wx.cloud) {
      throw new Error('云开发不可用')
    }
    
    try {
      wx.cloud.init({
        env: 'cloudbase-7g8nxwah43c62b19',
        traceUser: true,
      })
      
      // 测试云函数调用
      await wx.cloud.callFunction({
        name: 'quickTest',
        data: { test: true }
      })
      
      return true
    } catch (error) {
      console.warn('云函数测试失败，但云开发初始化可能成功:', error)
      return true // 即使云函数失败，初始化可能仍然成功
    }
  },

  // 清理缓存
  cleanCache() {
    try {
      // 清理可能的错误状态
      const keysToRemove = []
      
      // 获取所有存储的 key
      const info = wx.getStorageInfoSync()
      info.keys.forEach(key => {
        if (key.includes('error') || key.includes('cache') || key.includes('temp')) {
          keysToRemove.push(key)
        }
      })
      
      // 删除临时缓存
      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (e) {
          // 忽略删除失败
        }
      })
      
      console.log(`🗑️ 已清理 ${keysToRemove.length} 个缓存项`)
      return true
    } catch (error) {
      console.warn('缓存清理部分失败:', error)
      return false
    }
  },

  // 健康检查
  async healthCheck() {
    const checks = []
    
    // 检查云开发
    checks.push(this.checkCloud())
    
    // 检查存储
    checks.push(this.checkStorage())
    
    // 检查网络
    checks.push(this.checkNetwork())
    
    const results = await Promise.allSettled(checks)
    const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length
    
    console.log(`🏥 健康检查: ${successCount}/${results.length} 项通过`)
    
    return successCount >= 2 // 至少2项通过认为健康
  },

  // 检查云开发
  async checkCloud() {
    try {
      if (!wx.cloud) return false
      
      // 尝试调用一个简单的云函数
      await wx.cloud.callFunction({
        name: 'quickTest',
        data: { ping: true }
      })
      
      return true
    } catch (error) {
      console.warn('云开发检查失败:', error)
      return false
    }
  },

  // 检查存储
  checkStorage() {
    try {
      const testKey = 'healthCheck_' + Date.now()
      wx.setStorageSync(testKey, 'test')
      const value = wx.getStorageSync(testKey)
      wx.removeStorageSync(testKey)
      return value === 'test'
    } catch (error) {
      console.warn('存储检查失败:', error)
      return false
    }
  },

  // 检查网络
  async checkNetwork() {
    try {
      const networkInfo = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: resolve,
          fail: reject
        })
      })
      return networkInfo.networkType !== 'none'
    } catch (error) {
      console.warn('网络检查失败:', error)
      return false
    }
  },

  // 显示当前状态
  async showStatus() {
    console.log('📊 当前系统状态:')
    console.log('=====================================')
    
    // 云开发状态
    console.log('云开发:', wx.cloud ? '✅ 可用' : '❌ 不可用')
    
    // 用户登录状态
    const app = getApp()
    console.log('登录状态:', app.globalData.isLoggedIn ? '✅ 已登录' : '❌ 未登录')
    
    // 网络状态
    try {
      const networkInfo = await new Promise((resolve) => {
        wx.getNetworkType({
          success: resolve,
          fail: () => resolve({ networkType: 'unknown' })
        })
      })
      console.log('网络状态:', networkInfo.networkType)
    } catch (e) {
      console.log('网络状态: 检查失败')
    }
    
    // 存储使用情况
    try {
      const storageInfo = wx.getStorageInfoSync()
      console.log(`存储使用: ${storageInfo.keys.length} 个键, ${(storageInfo.currentSize/1024).toFixed(2)}KB`)
    } catch (e) {
      console.log('存储状态: 检查失败')
    }
    
    console.log('=====================================')
  }
}

// 导出快速修复工具
module.exports = QuickFix

// 如果在控制台环境，直接暴露到全局
if (typeof window !== 'undefined') {
  window.QuickFix = QuickFix
  console.log('💡 快速修复工具已加载，可使用以下命令:')
  console.log('QuickFix.fixAll() - 一键修复所有问题')
  console.log('QuickFix.showStatus() - 显示系统状态')
  console.log('QuickFix.healthCheck() - 执行健康检查')
}
