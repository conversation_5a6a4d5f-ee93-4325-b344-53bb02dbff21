<!--pages/chat/chat.wxml-->
<view class="container">
  <!-- 顶部工具栏 -->
  <view class="toolbar">
    <view class="model-selector" bindtap="toggleModelSelector">
      <view class="current-model">
        <text class="model-name">{{currentModelName}}</text>
        <text class="model-icon">{{showModelSelector ? '▲' : '▼'}}</text>
      </view>
    </view>
    
    <view class="toolbar-actions">
      <button class="action-btn" bindtap="clearMessages">
        <text class="action-icon">🗑️</text>
      </button>
    </view>
  </view>

  <!-- 模型选择器 -->
  <view class="model-dropdown {{showModelSelector ? 'show' : ''}}" wx:if="{{showModelSelector}}">
    <view 
      class="model-option {{item.available ? 'available' : 'unavailable'}}"
      wx:for="{{availableModels}}"
      wx:key="id"
      bindtap="selectModel"
      data-model="{{item.id}}"
    >
      <view class="model-info">
        <text class="model-name">{{item.name}}</text>
        <text class="model-status">{{item.available ? '可用' : '需升级'}}</text>
      </view>
      <view class="model-check" wx:if="{{item.id === currentModel}}">✓</view>
    </view>
  </view>

  <!-- 聊天消息区域 -->
  <scroll-view 
    class="messages-container" 
    scroll-y="true" 
    scroll-top="{{scrollTop}}"
    scroll-to-view="{{scrollToView}}"
    scroll-with-animation="true"
  >
    <!-- 快速问题（仅在没有消息时显示） -->
    <view class="quick-questions" wx:if="{{messages.length <= 1}}">
      <view class="quick-title">常见问题</view>
      <view class="questions-grid">
        <view 
          class="question-item"
          wx:for="{{quickQuestions}}"
          wx:key="index"
          bindtap="onQuickQuestionTap"
          data-question="{{item}}"
        >
          {{item}}
        </view>
      </view>
    </view>

    <!-- 消息列表 -->
    <view class="messages-list">
      <view 
        class="message-item {{item.type}}"
        wx:for="{{messages}}"
        wx:key="id"
        id="msg-{{index}}"
      >
        <!-- AI消息 -->
        <view class="message-ai" wx:if="{{item.type === 'ai'}}">
          <view class="ai-avatar">🤖</view>
          <view class="message-content">
            <view class="message-header">
              <text class="sender-name">IVD智能顾问</text>
              <text class="message-time">{{item.timestamp}}</text>
              <text class="model-tag" wx:if="{{item.model}}">{{item.modelName}}</text>
            </view>
            <view class="message-text {{item.isError ? 'error' : ''}}">{{item.content}}</view>
          </view>
        </view>

        <!-- 用户消息 -->
        <view class="message-user" wx:if="{{item.type === 'user'}}">
          <view class="message-content">
            <view class="message-header">
              <text class="message-time">{{item.timestamp}}</text>
            </view>
            <view class="message-text">{{item.content}}</view>
          </view>
          <view class="user-avatar">👤</view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="loading-message" wx:if="{{isLoading}}">
        <view class="ai-avatar">🤖</view>
        <view class="loading-content">
          <view class="loading-dots">
            <view class="dot"></view>
            <view class="dot"></view>
            <view class="dot"></view>
          </view>
          <text class="loading-text">AI正在思考中...</text>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 输入区域 -->
  <view class="input-area">
    <view class="input-container">
      <textarea
        class="message-input"
        placeholder="请输入您的问题..."
        value="{{inputText}}"
        bindinput="onInputChange"
        auto-height
        maxlength="500"
        show-confirm-bar="false"
      ></textarea>
      <button 
        class="send-btn {{inputText.trim() ? 'active' : ''}}"
        bindtap="sendMessage"
        disabled="{{isLoading || !inputText.trim()}}"
      >
        <text class="send-icon">{{isLoading ? '⏳' : '📤'}}</text>
      </button>
    </view>
    
    <!-- 使用提示 -->
    <view class="usage-tip">
      <text class="tip-text">剩余咨询次数：10次</text>
    </view>
  </view>
</view>
