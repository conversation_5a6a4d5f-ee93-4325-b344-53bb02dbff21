/* app.wxss */
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* 通用容器 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  padding: 30rpx;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
}

.btn-secondary {
  background: #ffffff;
  color: #1976D2;
  border: 2rpx solid #1976D2;
  border-radius: 50rpx;
  padding: 22rpx 46rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-outline {
  background: transparent;
  color: #666666;
  border: 2rpx solid #e0e0e0;
  border-radius: 50rpx;
  padding: 22rpx 46rpx;
  font-size: 28rpx;
}

/* 文本样式 */
.text-primary {
  color: #1976D2;
}

.text-secondary {
  color: #666666;
}

.text-muted {
  color: #999999;
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: 600;
}

/* 间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }

/* Flex布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  margin-left: 20rpx;
  color: #999999;
  font-size: 28rpx;
}

/* 消息气泡 */
.message-bubble {
  max-width: 80%;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  word-wrap: break-word;
}

.message-user {
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  color: #ffffff;
  align-self: flex-end;
  border-bottom-right-radius: 8rpx;
}

.message-ai {
  background: #ffffff;
  color: #333333;
  align-self: flex-start;
  border: 2rpx solid #f0f0f0;
  border-bottom-left-radius: 8rpx;
}
