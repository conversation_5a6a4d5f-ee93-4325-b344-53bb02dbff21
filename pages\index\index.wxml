<!--pages/index/index.wxml-->
<view class="container">
  <!-- 头部欢迎区域 -->
  <view class="header-section">
    <view class="welcome-card card">
      <view class="welcome-content">
        <view class="welcome-title">
          <text class="title-main">IVD智能顾问</text>
          <text class="title-sub">您的专属研发、注册、销售顾问</text>
        </view>
        <view class="welcome-desc">
          随时随地获得专业咨询，助力您的IVD产品成功
        </view>
      </view>
      <view class="welcome-action">
        <button class="btn-primary start-chat-btn" bindtap="startChat">
          <text class="btn-icon">💬</text>
          <text>开始咨询</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 功能特色区域 -->
  <view class="features-section">
    <view class="section-title">
      <text class="title-text">核心功能</text>
      <text class="title-desc">专业领域，精准服务</text>
    </view>
    
    <view class="features-grid">
      <view 
        class="feature-card" 
        wx:for="{{features}}" 
        wx:key="index"
        bindtap="onFeatureClick"
        data-index="{{index}}"
      >
        <view class="feature-icon" style="background-color: {{item.color}}20; color: {{item.color}}">
          {{item.icon}}
        </view>
        <view class="feature-content">
          <view class="feature-title">{{item.title}}</view>
          <view class="feature-desc">{{item.desc}}</view>
        </view>
        <view class="feature-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- AI模型展示区域 -->
  <view class="ai-models-section">
    <view class="section-title">
      <text class="title-text">AI模型</text>
      <text class="title-desc">多模型智能，专业可靠</text>
    </view>
    
    <view class="models-list">
      <view 
        class="model-item {{item.available ? 'available' : 'unavailable'}}" 
        wx:for="{{aiModels}}" 
        wx:key="name"
      >
        <view class="model-info">
          <view class="model-name">{{item.name}}</view>
          <view class="model-desc">{{item.desc}}</view>
        </view>
        <view class="model-status">
          <text class="status-text">{{item.available ? '可用' : '升级解锁'}}</text>
          <view class="status-dot {{item.available ? 'active' : 'inactive'}}"></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 订阅状态区域 -->
  <view class="subscription-section" wx:if="{{isLoggedIn}}">
    <view class="subscription-card card">
      <view class="subscription-header">
        <view class="subscription-title">当前套餐</view>
        <view class="subscription-type">免费版</view>
      </view>
      <view class="subscription-quota">
        <view class="quota-text">剩余咨询次数</view>
        <view class="quota-number">10次</view>
      </view>
      <button class="btn-outline upgrade-btn" bindtap="viewSubscription">
        升级套餐
      </button>
    </view>
  </view>

  <!-- 未登录提示 -->
  <view class="login-prompt card" wx:if="{{!isLoggedIn}}">
    <view class="prompt-content">
      <view class="prompt-icon">🔐</view>
      <view class="prompt-title">登录后享受更多功能</view>
      <view class="prompt-desc">保存聊天记录，管理订阅套餐</view>
    </view>
    <button class="btn-secondary" bindtap="viewSubscription">
      立即登录
    </button>
  </view>
</view>
