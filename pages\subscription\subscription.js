// pages/subscription/subscription.js
const app = getApp()

Page({
  data: {
    currentSubscription: {
      type: 'free',
      remainingQuota: 10,
      expiryDate: null
    },
    currentSubscriptionName: '免费版',
    plans: [
      {
        id: 'free',
        name: '免费版',
        price: 0,
        period: '永久',
        quota: 10,
        features: [
          '每月10次AI咨询',
          'DeepSeek-V3基础模型',
          '基础功能使用',
          '社区支持'
        ],
        color: '#4CAF50',
        recommended: false
      },
      {
        id: 'basic',
        name: '基础版',
        price: 29,
        period: '月',
        quota: 100,
        features: [
          '每月100次AI咨询',
          '所有AI模型访问',
          '聊天记录云同步',
          '优先客服支持',
          '专业模板库'
        ],
        color: '#2196F3',
        recommended: true
      },
      {
        id: 'premium',
        name: '高级版',
        price: 99,
        period: '月',
        quota: 500,
        features: [
          '每月500次AI咨询',
          '所有AI模型访问',
          '无限聊天记录',
          '专属客服支持',
          '高级分析报告',
          '定制化服务',
          'API接口访问'
        ],
        color: '#FF9800',
        recommended: false
      }
    ],
    selectedPlan: null,
    selectedPlanPrice: '',
    isLoading: false
  },

  onLoad() {
    this.loadSubscriptionData()
  },

  onShow() {
    this.loadSubscriptionData()
  },

  // 加载订阅数据
  loadSubscriptionData() {
    const subscription = app.globalData.subscription

    // 计算订阅类型显示名称
    let subscriptionName = '免费版'
    if (subscription.type === 'basic') {
      subscriptionName = '基础版'
    } else if (subscription.type === 'premium') {
      subscriptionName = '高级版'
    }

    this.setData({
      currentSubscription: subscription,
      currentSubscriptionName: subscriptionName
    })
  },

  // 选择套餐
  selectPlan(e) {
    const planId = e.currentTarget.dataset.plan
    const plan = this.data.plans.find(p => p.id === planId)

    // 计算价格显示文本
    const priceText = plan.price === 0 ? '免费' : `¥${plan.price}/${plan.period}`

    this.setData({
      selectedPlan: plan,
      selectedPlanPrice: priceText
    })
  },

  // 购买套餐
  purchasePlan() {
    if (!this.data.selectedPlan) {
      wx.showToast({
        title: '请先选择套餐',
        icon: 'none'
      })
      return
    }

    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后购买套餐',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }
        }
      })
      return
    }

    const plan = this.data.selectedPlan

    // 免费套餐直接激活
    if (plan.id === 'free') {
      this.activateFreePlan()
      return
    }

    // 付费套餐调用微信支付
    this.initiatePayment(plan)
  },

  // 激活免费套餐
  activateFreePlan() {
    wx.showModal({
      title: '确认激活',
      content: '确定要激活免费套餐吗？',
      success: (res) => {
        if (res.confirm) {
          // 更新订阅信息
          const newSubscription = {
            type: 'free',
            remainingQuota: 10,
            expiryDate: null
          }
          
          app.globalData.subscription = newSubscription
          this.setData({
            currentSubscription: newSubscription,
            currentSubscriptionName: '免费版',
            selectedPlan: null,
            selectedPlanPrice: ''
          })
          
          wx.showToast({
            title: '免费套餐已激活',
            icon: 'success'
          })
        }
      }
    })
  },

  // 发起支付
  initiatePayment(plan) {
    this.setData({ isLoading: true })

    // 调用云函数创建支付订单
    wx.cloud.callFunction({
      name: 'createPayment',
      data: {
        planId: plan.id,
        amount: plan.price * 100, // 转换为分
        description: `${plan.name} - ${plan.period}套餐`
      },
      success: (res) => {
        console.log('创建支付订单成功', res)
        
        if (res.result.success) {
          // 调用微信支付
          this.requestPayment(res.result.payment)
        } else {
          this.setData({ isLoading: false })
          wx.showToast({
            title: res.result.message || '创建订单失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('创建支付订单失败', err)
        this.setData({ isLoading: false })
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 调用微信支付
  requestPayment(paymentData) {
    wx.requestPayment({
      timeStamp: paymentData.timeStamp,
      nonceStr: paymentData.nonceStr,
      package: paymentData.package,
      signType: paymentData.signType,
      paySign: paymentData.paySign,
      success: (res) => {
        console.log('支付成功', res)
        this.handlePaymentSuccess()
      },
      fail: (err) => {
        console.error('支付失败', err)
        this.setData({ isLoading: false })
        
        if (err.errMsg === 'requestPayment:fail cancel') {
          wx.showToast({
            title: '支付已取消',
            icon: 'none'
          })
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none'
          })
        }
      }
    })
  },

  // 处理支付成功
  handlePaymentSuccess() {
    const plan = this.data.selectedPlan
    
    // 更新订阅信息
    const expiryDate = new Date()
    expiryDate.setMonth(expiryDate.getMonth() + 1)
    
    const newSubscription = {
      type: plan.id,
      remainingQuota: plan.quota,
      expiryDate: expiryDate.toISOString()
    }
    
    // 计算订阅类型显示名称
    let subscriptionName = '免费版'
    if (plan.id === 'basic') {
      subscriptionName = '基础版'
    } else if (plan.id === 'premium') {
      subscriptionName = '高级版'
    }

    app.globalData.subscription = newSubscription
    this.setData({
      currentSubscription: newSubscription,
      currentSubscriptionName: subscriptionName,
      selectedPlan: null,
      selectedPlanPrice: '',
      isLoading: false
    })
    
    wx.showModal({
      title: '购买成功',
      content: `恭喜您成功购买${plan.name}！现在可以享受更多功能了。`,
      showCancel: false,
      success: () => {
        // 可以跳转到聊天页面
        wx.switchTab({
          url: '/pages/chat/chat'
        })
      }
    })
  },

  // 查看套餐详情
  viewPlanDetails(e) {
    const planId = e.currentTarget.dataset.plan
    const plan = this.data.plans.find(p => p.id === planId)
    
    const featuresText = plan.features.join('\n• ')
    
    const priceText = plan.price === 0 ? '免费' : `¥${plan.price}/${plan.period}`

    wx.showModal({
      title: plan.name,
      content: `价格：${priceText}\n\n功能特性：\n• ${featuresText}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 联系客服
  contactSupport() {
    wx.showModal({
      title: '联系客服',
      content: '如有任何问题，请添加客服微信：ivd-support',
      confirmText: '复制微信号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'ivd-support',
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  }
})
