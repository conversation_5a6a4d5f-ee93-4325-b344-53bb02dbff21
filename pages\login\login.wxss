/* pages/login/login.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 头部区域 */
.header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx 40rpx;
}

.logo-section {
  text-align: center;
  color: #ffffff;
}

.logo {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
  display: block;
}

.app-desc {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
}

/* 登录卡片 */
.login-card {
  margin: 40rpx 40rpx 0;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 60rpx 40rpx 40rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  margin-bottom: 50rpx;
}

.title-main {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 12rpx;
}

.title-sub {
  display: block;
  font-size: 28rpx;
  color: #666666;
}

/* 功能预览 */
.features-preview {
  display: flex;
  justify-content: space-around;
  margin-bottom: 50rpx;
  padding: 30rpx 0;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.feature-icon {
  font-size: 36rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.feature-text {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

/* 登录操作 */
.login-actions {
  margin-bottom: 40rpx;
}

.login-btn {
  width: 100%;
  margin-bottom: 24rpx;
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  border: none;
  border-radius: 50rpx;
  padding: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
}

.login-btn[disabled] {
  opacity: 0.7;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}

.guest-btn {
  width: 100%;
  background: transparent;
  color: #666666;
  border: 2rpx solid #e0e0e0;
  border-radius: 50rpx;
  padding: 26rpx;
  font-size: 30rpx;
}

/* 用户协议 */
.agreement {
  text-align: center;
  margin-bottom: 30rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}

.link {
  color: #1976D2;
}

/* 底部信息 */
.bottom-info {
  padding: 40rpx;
  background: #ffffff;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  gap: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 36rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f7ff;
  border-radius: 50%;
  color: #1976D2;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.info-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

/* 返回按钮 */
.back-section {
  padding: 20rpx 40rpx 40rpx;
  background: #ffffff;
}

.back-btn {
  background: transparent;
  color: #666666;
  font-size: 28rpx;
  padding: 0;
  text-align: left;
}

.back-btn::after {
  border: none;
}
