# IVD智能顾问部署指南

## 部署前准备

### 1. 账号准备
- **微信小程序账号**: 已认证的企业或个人开发者账号
- **腾讯云账号**: 开通云开发服务
- **AI服务账号**: DeepSeek、阿里云、OpenAI等

### 2. 开发环境
- 微信开发者工具 (最新稳定版)
- Node.js 14.x 或更高版本
- Git 版本控制工具

## 第一步：微信小程序配置

### 1.1 创建小程序
1. 登录微信公众平台 (mp.weixin.qq.com)
2. 注册小程序账号或使用现有账号
3. 获取小程序 AppID: `wx40de5ae4b1c122b6`
4. 配置服务器域名（后续云开发会自动配置）

### 1.2 下载开发工具
1. 下载微信开发者工具
2. 使用微信扫码登录
3. 导入项目，填入 AppID

## 第二步：腾讯云开发环境配置

### 2.1 开通云开发
1. 登录腾讯云控制台
2. 搜索"云开发"并开通服务
3. 创建环境，环境ID: `cloudbase-7g8nxwah43c62b19`
4. 选择计费模式（建议按量付费）

### 2.2 环境配置
```bash
# 环境基本信息
环境名称: IVD智能顾问生产环境
环境ID: cloudbase-7g8nxwah43c62b19
地域: 广州
网络: 公网
```

### 2.3 权限设置
1. 在云开发控制台 -> 环境 -> 登录授权
2. 启用"微信登录"
3. 配置安全域名（小程序域名会自动添加）

## 第三步：云函数部署

### 3.1 初始化云开发
在微信开发者工具中：
1. 点击"云开发"按钮
2. 选择环境 `cloudbase-7g8nxwah43c62b19`
3. 开通云开发服务

### 3.2 部署云函数
逐个部署以下云函数：

#### login 函数
```bash
# 右键 cloudfunctions/login
# 选择：上传并部署：云端安装依赖
```

#### aiChat 函数
```bash
# 右键 cloudfunctions/aiChat  
# 选择：上传并部署：云端安装依赖
```

#### createPayment 函数
```bash
# 右键 cloudfunctions/createPayment
# 选择：上传并部署：云端安装依赖
```

#### payCallback 函数
```bash
# 右键 cloudfunctions/payCallback
# 选择：上传并部署：云端安装依赖
```

### 3.3 配置环境变量
在云开发控制台 -> 环境 -> 云函数 -> 环境变量：

```bash
# DeepSeek API配置
DEEPSEEK_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxx

# 通义千问API配置  
QWEN_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxx

# OpenAI API配置
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxx
```

## 第四步：数据库初始化

### 4.1 创建集合
在云开发控制台 -> 数据库 -> 集合管理：

1. **users** - 用户信息集合
2. **chatHistory** - 聊天记录集合  
3. **orders** - 订单信息集合

### 4.2 设置权限
为每个集合设置适当的读写权限：

```javascript
// users 集合权限
{
  "read": "auth.openid == resource.openid",
  "write": "auth.openid == resource.openid"
}

// chatHistory 集合权限
{
  "read": "auth.openid == resource.openid", 
  "write": "auth.openid == resource.openid"
}

// orders 集合权限
{
  "read": "auth.openid == resource.openid",
  "write": false  // 只允许云函数写入
}
```

### 4.3 创建索引
为提高查询性能，创建以下索引：

```javascript
// users 集合
{ "openid": 1 }

// chatHistory 集合  
{ "openid": 1, "timestamp": -1 }

// orders 集合
{ "openid": 1, "createTime": -1 }
{ "orderNo": 1 }
```

## 第五步：支付功能配置

### 5.1 开通微信支付
1. 登录微信支付商户平台
2. 完成商户认证
3. 获取商户号和API密钥

### 5.2 配置云支付
在云开发控制台 -> 云支付：
1. 开通云支付服务
2. 配置商户信息
3. 设置支付回调云函数: `payCallback`

### 5.3 测试支付功能
```javascript
// 使用测试环境验证支付流程
// 确保支付回调正常处理
// 验证订阅状态更新
```

## 第六步：AI服务配置

### 6.1 DeepSeek API
1. 注册 DeepSeek 账号
2. 获取 API Key
3. 配置到环境变量 `DEEPSEEK_API_KEY`

### 6.2 通义千问 API
1. 注册阿里云账号
2. 开通通义千问服务
3. 获取 API Key
4. 配置到环境变量 `QWEN_API_KEY`

### 6.3 OpenAI API
1. 注册 OpenAI 账号
2. 获取 API Key
3. 配置到环境变量 `OPENAI_API_KEY`

## 第七步：项目发布

### 7.1 代码审查
1. 检查所有功能是否正常
2. 验证支付流程
3. 测试AI对话功能
4. 确认用户登录流程

### 7.2 小程序提审
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台提交审核
4. 等待审核通过后发布

### 7.3 监控配置
1. 配置云开发监控告警
2. 设置云函数错误通知
3. 监控数据库使用情况
4. 关注支付状态异常

## 第八步：运维监控

### 8.1 性能监控
- 云函数执行时间和成功率
- 数据库读写性能
- AI API调用延迟
- 用户活跃度统计

### 8.2 错误处理
- 设置错误日志收集
- 配置告警通知
- 建立故障处理流程
- 定期备份重要数据

### 8.3 成本优化
- 监控云开发资源使用
- 优化云函数执行效率
- 合理设置数据库索引
- 控制AI API调用频率

## 常见问题解决

### Q1: 云函数部署失败
```bash
# 检查网络连接
# 确认依赖包版本兼容性
# 查看错误日志详细信息
```

### Q2: AI API调用失败
```bash
# 验证API Key是否正确
# 检查网络连接和防火墙
# 确认API配额是否充足
```

### Q3: 支付功能异常
```bash
# 检查商户号配置
# 验证支付回调地址
# 确认证书配置正确
```

### Q4: 数据库权限错误
```bash
# 检查集合权限设置
# 确认用户登录状态
# 验证openid获取正确
```

## 安全注意事项

1. **API密钥安全**: 使用环境变量存储，不要硬编码
2. **数据库权限**: 严格控制读写权限
3. **用户数据**: 遵守隐私保护法规
4. **支付安全**: 使用官方SDK，验证回调签名
5. **内容审核**: 对用户输入进行适当过滤

## 备份策略

1. **代码备份**: 使用Git版本控制
2. **数据库备份**: 定期导出重要数据
3. **配置备份**: 记录所有配置参数
4. **文档维护**: 及时更新部署文档

---

按照以上步骤完成部署后，IVD智能顾问小程序即可正常运行。如遇问题，请参考错误日志或联系技术支持。
