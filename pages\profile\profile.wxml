<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-card card" wx:if="{{isLoggedIn}}">
      <view class="user-info">
        <image 
          class="user-avatar" 
          src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}"
          mode="aspectFill"
        ></image>
        <view class="user-details">
          <view class="user-name">{{userInfo.nickName || '用户'}}</view>
          <view class="user-status">
            {{userInfo.isGuest ? '游客模式' : '已登录'}}
          </view>
        </view>
        <button class="edit-btn" bindtap="editProfile">
          <text class="edit-icon">✏️</text>
        </button>
      </view>
      
      <!-- 订阅状态 -->
      <view class="subscription-status" bindtap="viewSubscription">
        <view class="status-info">
          <view class="status-title">当前套餐</view>
          <view class="status-type">
            {{subscription.type === 'free' ? '免费版' : subscription.type === 'basic' ? '基础版' : '高级版'}}
          </view>
        </view>
        <view class="status-quota">
          <view class="quota-label">剩余次数</view>
          <view class="quota-value">{{subscription.remainingQuota}}</view>
        </view>
        <view class="status-arrow">→</view>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="login-prompt card" wx:if="{{!isLoggedIn}}">
      <view class="prompt-content">
        <view class="prompt-avatar">👤</view>
        <view class="prompt-text">
          <view class="prompt-title">未登录</view>
          <view class="prompt-desc">登录后享受完整功能</view>
        </view>
      </view>
      <button class="btn-primary login-btn" bindtap="goToLogin">
        立即登录
      </button>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-list">
      <view 
        class="menu-item"
        wx:for="{{menuItems}}"
        wx:key="title"
        bindtap="onMenuItemTap"
        data-index="{{index}}"
      >
        <view class="menu-icon">{{item.icon}}</view>
        <view class="menu-content">
          <view class="menu-title">{{item.title}}</view>
          <view class="menu-desc">{{item.desc}}</view>
        </view>
        <view class="menu-arrow">→</view>
      </view>
    </view>
  </view>

  <!-- 应用信息 -->
  <view class="app-info-section">
    <view class="info-card card">
      <view class="app-header">
        <view class="app-logo">🤖</view>
        <view class="app-details">
          <view class="app-name">IVD智能顾问</view>
          <view class="app-version">版本 2.0.0</view>
        </view>
      </view>
      
      <view class="app-description">
        专注于IVD行业的智能咨询服务，为您提供产品研发、注册申报、市场销售等专业指导。
      </view>
      
      <!-- 分享按钮 -->
      <view class="share-actions">
        <button 
          class="share-btn"
          open-type="share"
        >
          <text class="share-icon">📤</text>
          <text>分享给朋友</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section" wx:if="{{isLoggedIn}}">
    <button class="logout-btn" bindtap="logout">
      退出登录
    </button>
  </view>

  <!-- 底部版权信息 -->
  <view class="footer">
    <view class="copyright">
      <text class="copyright-text">© 2024 IVD智能顾问</text>
      <text class="copyright-desc">专业 · 可靠 · 智能</text>
    </view>
  </view>
</view>
