<!--pages/login/login.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="logo-section">
      <view class="logo">🤖</view>
      <view class="app-name">IVD智能顾问</view>
      <view class="app-desc">您的专属研发、注册、销售顾问</view>
    </view>
  </view>

  <!-- 登录卡片 -->
  <view class="login-card card">
    <view class="login-title">
      <text class="title-main">欢迎使用</text>
      <text class="title-sub">登录后享受完整功能</text>
    </view>

    <!-- 功能介绍 -->
    <view class="features-preview">
      <view class="feature-item">
        <view class="feature-icon">💬</view>
        <view class="feature-text">AI智能咨询</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📱</view>
        <view class="feature-text">聊天记录同步</view>
      </view>
      <view class="feature-item">
        <view class="feature-icon">⭐</view>
        <view class="feature-text">专属订阅服务</view>
      </view>
    </view>

    <!-- 登录按钮 -->
    <view class="login-actions">
      <button
        class="btn-primary login-btn"
        bindtap="onWechatLogin"
        disabled="{{isLoading}}"
      >
        <view class="btn-content">
          <view class="btn-icon" wx:if="{{!isLoading}}">🔐</view>
          <loading wx:if="{{isLoading}}" size="20px" color="#ffffff"></loading>
          <text class="btn-text">{{isLoading ? '登录中...' : '微信一键登录'}}</text>
        </view>
      </button>

      <button 
        class="btn-outline guest-btn"
        bindtap="onGuestMode"
        disabled="{{isLoading}}"
      >
        <text>快速体验（游客模式）</text>
      </button>
    </view>

    <!-- 用户协议 -->
    <view class="agreement">
      <text class="agreement-text">
        登录即表示同意
        <text class="link">《用户协议》</text>
        和
        <text class="link">《隐私政策》</text>
      </text>
    </view>
  </view>

  <!-- 底部说明 -->
  <view class="bottom-info">
    <view class="info-item">
      <view class="info-icon">🔒</view>
      <view class="info-content">
        <view class="info-title">安全保障</view>
        <view class="info-desc">微信官方授权，信息安全可靠</view>
      </view>
    </view>
    
    <view class="info-item">
      <view class="info-icon">⚡</view>
      <view class="info-content">
        <view class="info-title">快速便捷</view>
        <view class="info-desc">一键登录，无需注册繁琐流程</view>
      </view>
    </view>
  </view>

  <!-- 返回按钮 -->
  <view class="back-section">
    <button class="btn-text back-btn" bindtap="goBack">
      ← 返回首页
    </button>
  </view>
</view>
