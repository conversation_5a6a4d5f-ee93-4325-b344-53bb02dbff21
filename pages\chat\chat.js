// pages/chat/chat.js
const app = getApp()

Page({
  data: {
    messages: [],
    inputText: '',
    isLoading: false,
    currentModel: 'deepseek-v3-0324',
    availableModels: [
      { id: 'deepseek-v3-0324', name: 'DeepSeek-V3', available: true },
      { id: 'deepseek-r1-0528', name: 'DeepSeek-R1', available: true },
      { id: 'qwen3', name: 'Qwen3', available: false },
      { id: 'qwen-max', name: '<PERSON>wen Max', available: false },
      { id: 'chatgpt', name: 'ChatGPT', available: false }
    ],
    showModelSelector: false,
    scrollToView: '',
    quickQuestions: [
      '如何进行IVD产品的研发规划？',
      '注册申报需要准备哪些材料？',
      '市场销售策略有哪些要点？',
      '产品质量控制的关键环节是什么？'
    ]
  },

  onLoad() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showModal({
        title: '提示',
        content: '请先登录后使用AI咨询功能',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          } else {
            wx.switchTab({
              url: '/pages/index/index'
            })
          }
        }
      })
      return
    }

    // 初始化欢迎消息
    this.initWelcomeMessage()
  },

  onShow() {
    // 滚动到底部
    this.scrollToBottom()
  },

  // 初始化欢迎消息
  initWelcomeMessage() {
    const welcomeMessage = {
      id: Date.now(),
      type: 'ai',
      content: '您好！我是IVD智能顾问，专注于为您提供产品研发、注册申报和市场销售方面的专业咨询。请问有什么可以帮助您的吗？',
      timestamp: new Date().toLocaleTimeString(),
      model: this.data.currentModel
    }

    this.setData({
      messages: [welcomeMessage]
    })
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  // 发送消息
  sendMessage() {
    const content = this.data.inputText.trim()
    if (!content) {
      wx.showToast({
        title: '请输入消息内容',
        icon: 'none'
      })
      return
    }

    // 检查使用额度
    if (!this.checkQuota()) {
      return
    }

    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: content,
      timestamp: new Date().toLocaleTimeString()
    }

    this.setData({
      messages: [...this.data.messages, userMessage],
      inputText: '',
      isLoading: true
    })

    // 滚动到底部
    this.scrollToBottom()

    // 调用AI接口
    this.callAIAPI(content)
  },

  // 快速问题点击
  onQuickQuestionTap(e) {
    const question = e.currentTarget.dataset.question
    this.setData({
      inputText: question
    })
    this.sendMessage()
  },

  // 检查使用额度
  checkQuota() {
    const subscription = app.globalData.subscription
    if (subscription.remainingQuota <= 0) {
      wx.showModal({
        title: '使用额度不足',
        content: '您的咨询次数已用完，请升级套餐继续使用',
        confirmText: '升级套餐',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/subscription/subscription'
            })
          }
        }
      })
      return false
    }
    return true
  },

  // 调用AI API
  callAIAPI(content) {
    wx.cloud.callFunction({
      name: 'aiChat',
      data: {
        message: content,
        model: this.data.currentModel,
        context: this.getRecentMessages()
      },
      success: (res) => {
        console.log('AI响应成功', res)
        
        if (res.result.success) {
          // 添加AI回复
          const aiMessage = {
            id: Date.now() + 1,
            type: 'ai',
            content: res.result.reply,
            timestamp: new Date().toLocaleTimeString(),
            model: this.data.currentModel
          }

          this.setData({
            messages: [...this.data.messages, aiMessage],
            isLoading: false
          })

          // 更新使用额度
          this.updateQuota()
          
          // 滚动到底部
          this.scrollToBottom()
        } else {
          this.handleAIError(res.result.message)
        }
      },
      fail: (err) => {
        console.error('AI调用失败', err)
        this.handleAIError('网络连接失败，请重试')
      }
    })
  },

  // 获取最近的消息作为上下文
  getRecentMessages() {
    const recentCount = 6 // 最近6条消息
    const recentMessages = this.data.messages.slice(-recentCount)
    return recentMessages.map(msg => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content
    }))
  },

  // 处理AI错误
  handleAIError(message) {
    const errorMessage = {
      id: Date.now() + 1,
      type: 'ai',
      content: `抱歉，${message || '服务暂时不可用，请稍后重试'}`,
      timestamp: new Date().toLocaleTimeString(),
      model: this.data.currentModel,
      isError: true
    }

    this.setData({
      messages: [...this.data.messages, errorMessage],
      isLoading: false
    })

    this.scrollToBottom()
  },

  // 更新使用额度
  updateQuota() {
    const subscription = app.globalData.subscription
    subscription.remainingQuota = Math.max(0, subscription.remainingQuota - 1)
    app.globalData.subscription = subscription
  },

  // 滚动到底部
  scrollToBottom() {
    setTimeout(() => {
      this.setData({
        scrollToView: `msg-${this.data.messages.length - 1}`
      })
    }, 100)
  },

  // 切换模型选择器
  toggleModelSelector() {
    this.setData({
      showModelSelector: !this.data.showModelSelector
    })
  },

  // 选择AI模型
  selectModel(e) {
    const modelId = e.currentTarget.dataset.model
    const model = this.data.availableModels.find(m => m.id === modelId)
    
    if (!model.available) {
      wx.showModal({
        title: '模型不可用',
        content: '该模型需要升级套餐后才能使用',
        confirmText: '升级套餐',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/subscription/subscription'
            })
          }
        }
      })
      return
    }

    this.setData({
      currentModel: modelId,
      showModelSelector: false
    })

    wx.showToast({
      title: `已切换到${model.name}`,
      icon: 'success'
    })
  },

  // 清空聊天记录
  clearMessages() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.initWelcomeMessage()
          wx.showToast({
            title: '已清空聊天记录',
            icon: 'success'
          })
        }
      }
    })
  }
})
