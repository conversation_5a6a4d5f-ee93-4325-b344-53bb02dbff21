// 云函数：更新用户信息
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('更新用户信息云函数开始执行', { event })
    
    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID
    
    console.log('用户openid:', openid)
    
    if (!openid) {
      return {
        success: false,
        message: '用户身份验证失败'
      }
    }
    
    const { nickName, avatarUrl } = event
    
    // 验证参数
    if (!nickName && !avatarUrl) {
      return {
        success: false,
        message: '请提供要更新的用户信息'
      }
    }
    
    // 构建更新数据
    const updateData = {
      lastUpdateTime: new Date()
    }
    
    if (nickName) {
      updateData.nickName = nickName
    }
    
    if (avatarUrl) {
      updateData.avatarUrl = avatarUrl
    }
    
    console.log('准备更新的数据:', updateData)
    
    // 更新用户信息
    const updateResult = await db.collection('users').where({
      openid: openid
    }).update({
      data: updateData
    })
    
    console.log('用户信息更新结果:', updateResult)
    
    if (updateResult.stats.updated > 0) {
      // 获取更新后的用户信息
      const userQuery = await db.collection('users').where({
        openid: openid
      }).get()
      
      const userData = userQuery.data[0]
      
      return {
        success: true,
        message: '用户信息更新成功',
        userInfo: {
          nickName: userData.nickName,
          avatarUrl: userData.avatarUrl,
          openid: openid
        }
      }
    } else {
      return {
        success: false,
        message: '用户信息更新失败，用户不存在'
      }
    }
    
  } catch (error) {
    console.error('更新用户信息失败:', error)
    return {
      success: false,
      message: '更新失败，请重试',
      error: error.message
    }
  }
}
