// 错误监控和自动修复工具
const ErrorMonitor = {
  // 错误统计
  errorStats: {
    wxfile: 0,
    backgroundfetch: 0,
    cloudFunction: 0,
    other: 0
  },

  // 初始化错误监控
  init() {
    console.log('🔍 错误监控系统启动')
    
    // 重写 console.error 来捕获错误
    const originalError = console.error
    console.error = (...args) => {
      this.analyzeError(args.join(' '))
      originalError.apply(console, args)
    }
    
    // 监听未捕获的 Promise 错误
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        this.analyzeError(event.reason)
      })
    }
    
    console.log('✅ 错误监控系统已启动')
  },

  // 分析错误类型
  analyzeError(errorMsg) {
    const msg = errorMsg.toString().toLowerCase()
    
    if (msg.includes('wxfile') || msg.includes('miniprogramlop')) {
      this.errorStats.wxfile++
      this.handleWxfileError(errorMsg)
    } else if (msg.includes('backgroundfetch') || msg.includes('privacy fail')) {
      this.errorStats.backgroundfetch++
      this.handleBackgroundfetchError(errorMsg)
    } else if (msg.includes('cloud.callfunction') || msg.includes('云函数')) {
      this.errorStats.cloudFunction++
      this.handleCloudFunctionError(errorMsg)
    } else {
      this.errorStats.other++
    }
  },

  // 处理 wxfile 错误
  handleWxfileError(error) {
    console.warn('🔧 自动处理 wxfile 错误:', error)
    // 这类错误通常可以忽略，不影响功能
    return true
  },

  // 处理后台获取隐私错误
  handleBackgroundfetchError(error) {
    console.warn('🔧 自动处理 backgroundfetch 错误:', error)
    
    // 尝试修复：检查隐私设置
    try {
      if (wx.getPrivacySetting) {
        wx.getPrivacySetting({
          success: (res) => {
            console.log('隐私设置状态:', res)
            if (!res.needAuthorization) {
              console.log('✅ 隐私设置正常')
            }
          },
          fail: (err) => {
            console.warn('获取隐私设置失败:', err)
          }
        })
      }
    } catch (e) {
      console.warn('隐私设置检查失败:', e)
    }
    
    return true
  },

  // 处理云函数错误
  handleCloudFunctionError(error) {
    console.warn('🔧 自动处理云函数错误:', error)
    
    // 尝试重新初始化云开发
    try {
      if (wx.cloud) {
        wx.cloud.init({
          env: 'cloudbase-7g8nxwah43c62b19',
          traceUser: true,
        })
        console.log('🔄 云开发重新初始化完成')
      }
    } catch (e) {
      console.error('云开发重新初始化失败:', e)
    }
    
    return false // 云函数错误需要关注
  },

  // 获取错误报告
  getErrorReport() {
    const total = Object.values(this.errorStats).reduce((sum, count) => sum + count, 0)
    
    console.log('📊 错误统计报告:')
    console.log('=====================================')
    console.log(`wxfile 错误: ${this.errorStats.wxfile} 次`)
    console.log(`backgroundfetch 错误: ${this.errorStats.backgroundfetch} 次`)
    console.log(`云函数错误: ${this.errorStats.cloudFunction} 次`)
    console.log(`其他错误: ${this.errorStats.other} 次`)
    console.log(`总计: ${total} 次`)
    console.log('=====================================')
    
    // 提供修复建议
    this.provideSuggestions()
    
    return this.errorStats
  },

  // 提供修复建议
  provideSuggestions() {
    const suggestions = []
    
    if (this.errorStats.wxfile > 0) {
      suggestions.push('💡 wxfile 错误已自动过滤，不影响功能')
    }
    
    if (this.errorStats.backgroundfetch > 0) {
      suggestions.push('💡 建议检查小程序隐私设置配置')
    }
    
    if (this.errorStats.cloudFunction > 0) {
      suggestions.push('💡 建议检查云函数部署状态和网络连接')
      suggestions.push('💡 可尝试重新部署相关云函数')
    }
    
    if (this.errorStats.other > 0) {
      suggestions.push('💡 存在其他类型错误，建议详细检查日志')
    }
    
    if (suggestions.length > 0) {
      console.log('🔧 修复建议:')
      suggestions.forEach((suggestion, index) => {
        console.log(`${index + 1}. ${suggestion}`)
      })
    } else {
      console.log('🎉 暂无错误，系统运行正常！')
    }
  },

  // 重置错误统计
  reset() {
    this.errorStats = {
      wxfile: 0,
      backgroundfetch: 0,
      cloudFunction: 0,
      other: 0
    }
    console.log('🔄 错误统计已重置')
  },

  // 执行系统健康检查
  async healthCheck() {
    console.log('🏥 开始系统健康检查...')
    
    const health = {
      cloudInit: false,
      cloudFunction: false,
      storage: false,
      network: false
    }
    
    // 检查云开发初始化
    try {
      if (wx.cloud) {
        health.cloudInit = true
        console.log('✅ 云开发初始化正常')
      }
    } catch (e) {
      console.error('❌ 云开发初始化异常:', e)
    }
    
    // 检查云函数
    try {
      const result = await wx.cloud.callFunction({
        name: 'quickTest',
        data: { healthCheck: true }
      })
      health.cloudFunction = true
      console.log('✅ 云函数调用正常')
    } catch (e) {
      console.warn('⚠️ 云函数调用异常:', e)
    }
    
    // 检查本地存储
    try {
      wx.setStorageSync('healthCheck', Date.now())
      const stored = wx.getStorageSync('healthCheck')
      if (stored) {
        health.storage = true
        console.log('✅ 本地存储正常')
        wx.removeStorageSync('healthCheck')
      }
    } catch (e) {
      console.error('❌ 本地存储异常:', e)
    }
    
    // 检查网络
    try {
      const networkInfo = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: resolve,
          fail: reject
        })
      })
      health.network = networkInfo.networkType !== 'none'
      console.log('✅ 网络连接正常:', networkInfo.networkType)
    } catch (e) {
      console.error('❌ 网络连接异常:', e)
    }
    
    const healthScore = Object.values(health).filter(Boolean).length
    console.log(`🏥 系统健康评分: ${healthScore}/4`)
    
    return health
  }
}

// 自动启动错误监控
ErrorMonitor.init()

module.exports = ErrorMonitor
