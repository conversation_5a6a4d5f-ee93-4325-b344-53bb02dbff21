# IVD智能顾问微信小程序

## 项目概述

IVD智能顾问是一款专注于IVD（体外诊断）行业的智能咨询微信小程序，为用户提供产品研发、注册申报、市场销售等专业指导服务。

### 项目信息
- **项目名称**: IVD智能顾问 (ivd-intelligent-advisor)
- **版本**: 2.0.0
- **小程序AppID**: wx40de5ae4b1c122b6
- **开发框架**: 微信小程序原生框架
- **云服务**: 腾讯云开发 (CloudBase)
- **环境ID**: cloudbase-7g8nxwah43c62b19

## 核心功能

### 🤖 多AI模型集成
- **DeepSeek-V3-0324**: 默认基础模型，免费用户可用
- **DeepSeek-R1-0528**: 逻辑推理专家模型
- **Qwen3**: 中文理解优秀的通义千问模型
- **Qwen Max**: 通义千问高级版本
- **ChatGPT**: OpenAI的通用对话模型

### 💬 智能对话系统
- 专业的IVD领域知识问答
- 上下文感知的连续对话
- 实时AI响应和交互

### 👤 用户认证与管理
- 微信一键登录
- 游客模式快速体验
- 用户信息自动同步

### 💳 分级订阅服务
- **免费版**: 每月10次咨询，基础模型
- **基础版**: 每月100次咨询，所有模型访问
- **高级版**: 每月500次咨询，专属服务

### 💰 微信支付集成
- 安全的微信官方支付
- 自动订阅管理
- 支付状态实时更新

## 技术架构

### 前端技术
- 微信小程序原生框架
- WXML + WXSS + JavaScript
- 响应式设计，适配各种屏幕

### 后端服务
- 腾讯云开发 (CloudBase)
- 云函数处理业务逻辑
- 云数据库存储用户数据
- 云存储管理静态资源

### AI服务集成
- DeepSeek API
- 通义千问 API
- OpenAI API
- 环境变量安全管理API密钥

## 项目结构

```
ivd-intelligent-advisor/
├── app.js                 # 小程序入口文件
├── app.json               # 小程序配置文件
├── app.wxss               # 全局样式文件
├── project.config.json    # 项目配置文件
├── pages/                 # 页面目录
│   ├── index/            # 首页
│   ├── chat/             # AI聊天页面
│   ├── login/            # 登录页面
│   ├── profile/          # 个人中心
│   └── subscription/     # 订阅管理
├── images/               # 图片资源
├── cloudfunctions/       # 云函数目录
│   ├── login/           # 用户登录
│   ├── aiChat/          # AI聊天处理
│   ├── createPayment/   # 创建支付订单
│   └── payCallback/     # 支付回调处理
└── docs/                # 项目文档
```

## 快速开始

### 环境要求
- 微信开发者工具
- Node.js 14+
- 腾讯云开发账号

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd ivd-intelligent-advisor
   ```

2. **配置小程序**
   - 在微信开发者工具中导入项目
   - 修改 `project.config.json` 中的 `appid`
   - 配置云开发环境ID

3. **部署云函数**
   ```bash
   # 在微信开发者工具中
   # 右键云函数目录 -> 上传并部署：云端安装依赖
   ```

4. **配置环境变量**
   在云开发控制台设置以下环境变量：
   ```
   DEEPSEEK_API_KEY=your_deepseek_api_key
   QWEN_API_KEY=your_qwen_api_key
   OPENAI_API_KEY=your_openai_api_key
   ```

5. **初始化数据库**
   创建以下集合：
   - `users` - 用户信息
   - `chatHistory` - 聊天记录
   - `orders` - 订单信息

## 数据库设计

### users 集合
```javascript
{
  _id: "用户ID",
  openid: "微信openid",
  unionid: "微信unionid",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  subscription: {
    type: "free|basic|premium",
    remainingQuota: 10,
    expiryDate: "2024-12-31"
  },
  createTime: "创建时间",
  lastLoginTime: "最后登录时间",
  totalUsage: 0
}
```

### chatHistory 集合
```javascript
{
  _id: "记录ID",
  openid: "用户openid",
  userMessage: "用户消息",
  aiReply: "AI回复",
  model: "使用的AI模型",
  timestamp: "时间戳"
}
```

### orders 集合
```javascript
{
  _id: "订单ID",
  orderNo: "订单号",
  openid: "用户openid",
  planId: "套餐ID",
  amount: 2900,
  description: "订单描述",
  status: "pending|paid|failed",
  createTime: "创建时间",
  payTime: "支付时间"
}
```

## API接口说明

### 云函数接口

#### login - 用户登录
```javascript
// 请求参数
{
  code: "微信登录code",
  userInfo: {
    nickName: "用户昵称",
    avatarUrl: "头像URL"
  }
}

// 返回结果
{
  success: true,
  openid: "用户openid",
  userInfo: {...},
  subscription: {...}
}
```

#### aiChat - AI聊天
```javascript
// 请求参数
{
  message: "用户消息",
  model: "deepseek-v3-0324",
  context: [...]
}

// 返回结果
{
  success: true,
  reply: "AI回复内容",
  model: "使用的模型",
  remainingQuota: 9
}
```

## 部署指南

### 1. 云开发环境配置
1. 登录腾讯云控制台
2. 开通云开发服务
3. 创建环境：`cloudbase-7g8nxwah43c62b19`
4. 配置域名和权限

### 2. 云函数部署
1. 在微信开发者工具中打开云开发控制台
2. 逐个上传云函数并安装依赖
3. 配置环境变量和触发器

### 3. 数据库初始化
1. 创建所需集合
2. 设置索引和权限
3. 导入初始数据（如有）

### 4. 支付配置
1. 开通微信支付
2. 配置商户号和密钥
3. 设置支付回调地址

## 使用说明

### 用户端操作
1. **注册登录**: 微信一键登录或游客模式
2. **选择模型**: 根据订阅类型选择AI模型
3. **开始咨询**: 输入问题获取专业建议
4. **升级套餐**: 购买更高级的订阅服务

### 管理端功能
- 用户数据统计
- 使用情况分析
- 订阅管理
- 内容审核

## 常见问题

### Q: 如何获取AI API密钥？
A: 分别到DeepSeek、阿里云、OpenAI官网注册并申请API密钥。

### Q: 支付功能如何测试？
A: 使用微信支付沙箱环境进行测试，确保功能正常后切换到生产环境。

### Q: 如何添加新的AI模型？
A: 在 `aiChat` 云函数中添加新模型配置，并更新前端模型列表。

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 联系我们

- 项目维护者：IVD智能顾问团队
- 技术支持：<EMAIL>
- 官方网站：https://ivd-advisor.example.com

---

**注意**: 本项目仅供学习和参考使用，商业使用请确保遵守相关法律法规和平台规则。
