// 云函数：支付回调处理
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    const { orderNo, resultCode, openid } = event
    
    console.log('支付回调:', event)
    
    if (resultCode === 'SUCCESS') {
      // 支付成功，更新订单状态
      const orderQuery = await db.collection('orders').where({
        orderNo: orderNo
      }).get()
      
      if (orderQuery.data.length === 0) {
        console.error('订单不存在:', orderNo)
        return { success: false, message: '订单不存在' }
      }
      
      const order = orderQuery.data[0]
      
      // 更新订单状态
      await db.collection('orders').doc(order._id).update({
        data: {
          status: 'paid',
          payTime: new Date()
        }
      })
      
      // 更新用户订阅信息
      await updateUserSubscription(order.openid, order.planId)
      
      console.log('支付成功处理完成:', orderNo)
      return { success: true }
      
    } else {
      // 支付失败，更新订单状态
      await db.collection('orders').where({
        orderNo: orderNo
      }).update({
        data: {
          status: 'failed',
          failTime: new Date()
        }
      })
      
      console.log('支付失败:', orderNo)
      return { success: false, message: '支付失败' }
    }
    
  } catch (error) {
    console.error('支付回调处理失败:', error)
    return { success: false, message: '回调处理失败' }
  }
}

// 更新用户订阅信息
async function updateUserSubscription(openid, planId) {
  try {
    const planConfig = {
      'basic': {
        type: 'basic',
        quota: 100
      },
      'premium': {
        type: 'premium',
        quota: 500
      }
    }
    
    const plan = planConfig[planId]
    if (!plan) {
      console.error('未知的套餐类型:', planId)
      return
    }
    
    // 计算到期时间（一个月后）
    const expiryDate = new Date()
    expiryDate.setMonth(expiryDate.getMonth() + 1)
    
    // 更新用户订阅信息
    await db.collection('users').where({
      openid: openid
    }).update({
      data: {
        'subscription.type': plan.type,
        'subscription.remainingQuota': plan.quota,
        'subscription.expiryDate': expiryDate.toISOString(),
        subscriptionUpdateTime: new Date()
      }
    })
    
    console.log('用户订阅更新成功:', openid, planId)
    
  } catch (error) {
    console.error('更新用户订阅失败:', error)
  }
}
