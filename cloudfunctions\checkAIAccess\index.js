// 云函数：检查AI模型访问权限
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  try {
    console.log('检查AI访问权限云函数开始执行', { event })
    
    // 获取微信上下文
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID
    
    console.log('用户openid:', openid)
    
    if (!openid) {
      return {
        success: false,
        hasAccess: false,
        message: '用户身份验证失败，请重新登录'
      }
    }
    
    // 查询用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        hasAccess: false,
        message: '用户不存在，请重新登录'
      }
    }
    
    const userData = userQuery.data[0]
    const subscription = userData.subscription || {
      type: 'free',
      remainingQuota: 0,
      expiryDate: null
    }
    
    console.log('用户订阅信息:', subscription)
    
    // 检查访问权限
    let hasAccess = false
    let message = ''
    let quotaInfo = {
      current: subscription.remainingQuota || 0,
      total: 0,
      type: subscription.type
    }
    
    // 根据订阅类型检查权限
    switch (subscription.type) {
      case 'free':
        quotaInfo.total = 10
        if (subscription.remainingQuota > 0) {
          hasAccess = true
          message = `免费额度剩余 ${subscription.remainingQuota} 次`
        } else {
          hasAccess = false
          message = '免费额度已用完，请升级订阅'
        }
        break
        
      case 'basic':
        quotaInfo.total = 100
        if (subscription.expiryDate && new Date(subscription.expiryDate) > new Date()) {
          if (subscription.remainingQuota > 0) {
            hasAccess = true
            message = `基础版剩余 ${subscription.remainingQuota} 次`
          } else {
            hasAccess = false
            message = '本月额度已用完，请等待下月重置或升级订阅'
          }
        } else {
          hasAccess = false
          message = '基础版已过期，请续费'
        }
        break
        
      case 'premium':
        quotaInfo.total = 500
        if (subscription.expiryDate && new Date(subscription.expiryDate) > new Date()) {
          if (subscription.remainingQuota > 0) {
            hasAccess = true
            message = `高级版剩余 ${subscription.remainingQuota} 次`
          } else {
            hasAccess = false
            message = '本月额度已用完，请等待下月重置'
          }
        } else {
          hasAccess = false
          message = '高级版已过期，请续费'
        }
        break
        
      case 'unlimited':
        quotaInfo.total = -1 // 无限制
        if (subscription.expiryDate && new Date(subscription.expiryDate) > new Date()) {
          hasAccess = true
          message = '无限版，无使用限制'
        } else {
          hasAccess = false
          message = '无限版已过期，请续费'
        }
        break
        
      default:
        hasAccess = false
        message = '未知订阅类型'
    }
    
    console.log('权限检查结果:', { hasAccess, message, quotaInfo })
    
    return {
      success: true,
      hasAccess,
      message,
      quotaInfo,
      userInfo: {
        openid: userData.openid,
        nickName: userData.nickName,
        subscription: subscription
      }
    }
    
  } catch (error) {
    console.error('检查AI访问权限失败:', error)
    return {
      success: false,
      hasAccess: false,
      message: '权限检查失败，请重试',
      error: error.message
    }
  }
}
